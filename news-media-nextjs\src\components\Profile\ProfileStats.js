'use client';

import { useProfile } from '@/contexts/ProfileContext';
import { useState } from 'react';

export default function ProfileStats() {
  const { userData } = useProfile();
  const [showFollowersModal, setShowFollowersModal] = useState(false);
  const [showFollowingModal, setShowFollowingModal] = useState(false);

  // Format numbers for display (e.g., 1500 -> 1.5K)
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return num.toString();
  };

  const stats = [
    {
      value: formatNumber(userData.stats.posts),
      label: 'Posts',
      onClick: () => alert('View all posts')
    },
    {
      value: formatNumber(userData.stats.followers),
      label: 'Followers',
      onClick: () => setShowFollowersModal(true)
    },
    {
      value: formatNumber(userData.stats.following),
      label: 'Following',
      onClick: () => setShowFollowingModal(true)
    },
    {
      value: formatNumber(userData.stats.challenges),
      label: 'Challenges',
      onClick: () => alert('View all challenges')
    },
    {
      value: formatNumber(userData.stats.loves),
      label: 'Loves',
      onClick: () => alert('View all loved posts')
    },
  ];

  return (
    <>
      <div className="flex overflow-x-auto mb-10 pb-2 gap-8 justify-between">
        <div className="flex gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center cursor-pointer group"
              onClick={stat.onClick}
            >
              <div className="text-2xl font-bold group-hover:text-primary-600 transition-colors">
                {stat.value}
              </div>
              <div className="text-gray-600 text-sm">{stat.label}</div>
            </div>
          ))}
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex -space-x-2">
            {userData.followers.slice(0, 3).map((follower, index) => (
              <img
                key={index}
                src={follower.profilePic}
                alt={`${follower.username}'s profile`}
                className="w-8 h-8 rounded-full border-2 border-white"
              />
            ))}
          </div>
          <div className="text-sm text-gray-600">
            {userData.followers.length > 0 ? (
              <>
                <span className="font-bold text-gray-800">{userData.followers[0].username}</span>
                {userData.followers.length > 1 && (
                  <>
                    {' '}and{' '}
                    <span
                      className="font-bold text-gray-800 cursor-pointer hover:text-primary-600"
                      onClick={() => setShowFollowersModal(true)}
                    >
                      {userData.followers.length - 1} {userData.followers.length === 2 ? 'other' : 'others'}
                    </span>
                  </>
                )}
                {' '}follow you
              </>
            ) : (
              <span>No followers yet</span>
            )}
          </div>
        </div>
      </div>

      {/* Followers Modal */}
      {showFollowersModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setShowFollowersModal(false)}>
          <div className="bg-white rounded-lg w-full max-w-md p-6 relative" onClick={e => e.stopPropagation()}>
            <h2 className="text-xl font-bold mb-4">Followers</h2>
            <div className="max-h-80 overflow-y-auto">
              {userData.followers.length > 0 ? (
                <ul className="space-y-3">
                  {userData.followers.map((follower, index) => (
                    <li key={index} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-md">
                      <img
                        src={follower.profilePic}
                        alt={`${follower.username}'s profile`}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <p className="font-medium">{follower.username}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-center py-8">No followers yet</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Following Modal */}
      {showFollowingModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setShowFollowingModal(false)}>
          <div className="bg-white rounded-lg w-full max-w-md p-6 relative" onClick={e => e.stopPropagation()}>
            <h2 className="text-xl font-bold mb-4">Following</h2>
            <div className="max-h-80 overflow-y-auto">
              <p className="text-gray-500 text-center py-8">Following list coming soon</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
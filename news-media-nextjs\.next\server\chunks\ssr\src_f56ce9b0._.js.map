{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/UnauthGuard.jsx"], "sourcesContent": ["\"use client\"\nimport { useRouter } from \"next/navigation\";\nimport { useEffect, useState } from \"react\";\n\n/**\n * UnauthGuard - Prevents authenticated users from accessing auth pages\n * Redirects to home page if user is already logged in\n */\nfunction UnauthGuard({ children }) {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for token in localStorage (client-side only)\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token');\n      // If token exists and is not 'null', redirect to home page\n      if (token && token !== 'null') {\n        router.push('/');\n      }\n      setIsLoading(false);\n    }\n  }, [router]);\n\n  // Show nothing while checking authentication\n  if (isLoading) {\n    return null;\n  }\n\n  // If we get here, user is not authenticated, so show the children\n  return <>{children}</>;\n}\n\nexport default UnauthGuard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIA;;;CAGC,GACD,SAAS,YAAY,EAAE,QAAQ,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,uCAAmC;;QAOnC;IACF,GAAG;QAAC;KAAO;IAEX,6CAA6C;IAC7C,IAAI,WAAW;QACb,OAAO;IACT;IAEA,kEAAkE;IAClE,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/app/%28auth%29/layout.jsx"], "sourcesContent": ["'use client';\nimport UnauthGuard from \"@/components/UnauthGuard\";\n\n/**\n * Auth Layout - Wraps all authentication pages with UnauthGuard\n * This prevents authenticated users from accessing auth pages\n */\nexport default function AuthLayout({ children }) {\n  return <UnauthGuard>{children}</UnauthGuard>;\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAOe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBAAO,8OAAC,iIAAA,CAAA,UAAW;kBAAE;;;;;;AACvB", "debugId": null}}]}
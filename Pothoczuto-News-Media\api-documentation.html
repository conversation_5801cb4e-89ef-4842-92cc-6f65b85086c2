<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pothoczuto News Media API Documentation</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --code-bg: #f8f9fa;
            --border-color: #ddd;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background-color: var(--secondary-color);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .nav-menu {
            list-style: none;
            padding: 20px 0;
        }

        .nav-item {
            padding: 0 20px;
        }

        .nav-link {
            display: block;
            padding: 10px 15px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-section {
            margin-top: 15px;
            padding: 0 20px;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.5);
        }

        .main-content {
            flex: 1;
            padding: 30px;
            margin-left: 280px;
        }

        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        h1, h2, h3, h4 {
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        h1 {
            font-size: 2.5rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 30px;
        }

        h2 {
            font-size: 1.8rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        h3 {
            font-size: 1.4rem;
            margin-top: 30px;
        }

        h4 {
            font-size: 1.2rem;
            margin-top: 25px;
        }

        p {
            margin-bottom: 15px;
        }

        .endpoint {
            background-color: var(--code-bg);
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .endpoint-title {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .method {
            padding: 5px 10px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
            font-size: 0.8rem;
        }

        .get { background-color: var(--primary-color); }
        .post { background-color: var(--success-color); }
        .put { background-color: var(--warning-color); }
        .delete { background-color: var(--danger-color); }
        .patch { background-color: #9b59b6; }

        .url {
            font-family: monospace;
            font-size: 1rem;
        }

        .description {
            margin: 10px 0;
        }

        pre {
            background-color: var(--code-bg);
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid var(--border-color);
        }

        code {
            font-family: 'Courier New', Courier, monospace;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .badge-primary { background-color: var(--primary-color); color: white; }
        .badge-secondary { background-color: var(--secondary-color); color: white; }
        .badge-success { background-color: var(--success-color); color: white; }
        .badge-warning { background-color: var(--warning-color); color: white; }
        .badge-danger { background-color: var(--danger-color); color: white; }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--light-color);
            font-weight: bold;
        }

        tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .footer {
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            color: #777;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h1 style="color: white;">📰 Pothoczuto</h1>
                <p>News Media API Documentation</p>
            </div>
            <ul class="nav-menu">
                <li class="nav-section">Getting Started</li>
                <li class="nav-item"><a href="#introduction" class="nav-link">Introduction</a></li>
                <li class="nav-item"><a href="#authentication" class="nav-link">Authentication</a></li>

                <li class="nav-section">API Endpoints</li>
                <li class="nav-item"><a href="#auth-endpoints" class="nav-link">Authentication</a></li>
                <li class="nav-item"><a href="#blog-endpoints" class="nav-link">Blog Posts</a></li>
                <li class="nav-item"><a href="#comment-endpoints" class="nav-link">Comments</a></li>
                <li class="nav-item"><a href="#category-endpoints" class="nav-link">Categories</a></li>
                <li class="nav-item"><a href="#challenge-endpoints" class="nav-link">Challenges</a></li>
                <li class="nav-item"><a href="#chat-endpoints" class="nav-link">Chat</a></li>
                <li class="nav-item"><a href="#follow-endpoints" class="nav-link">Follow System</a></li>
                <li class="nav-item"><a href="#vote-endpoints" class="nav-link">Voting</a></li>
                <li class="nav-item"><a href="#love-endpoints" class="nav-link">Post Love</a></li>
                <li class="nav-item"><a href="#notification-endpoints" class="nav-link">Notifications</a></li>
                <li class="nav-item"><a href="#activity-endpoints" class="nav-link">Activity Logs</a></li>

                <li class="nav-section">Additional Information</li>
                <li class="nav-item"><a href="#frontend-integration" class="nav-link">Frontend Integration Guide</a></li>
                <li class="nav-item"><a href="#realtime" class="nav-link">Real-time Features</a></li>
                <li class="nav-item"><a href="#ai-integration" class="nav-link">AI Integration</a></li>
                <li class="nav-item"><a href="#error-handling" class="nav-link">Error Handling</a></li>
            </ul>
        </div>

        <div class="main-content">
            <section id="introduction" class="section">
                <h1>Pothoczuto News Media API Documentation</h1>
                <p>Welcome to the Pothoczuto News Media API documentation. This API provides a comprehensive set of endpoints for building a modern news and blog platform with features like user authentication, content management, social interactions, and real-time communication.</p>

                <h3>Base URL</h3>
                <pre><code>https://pothoczuto-news-media.onrender.com/api</code></pre>

                <h3>Technologies Used</h3>
                <div>
                    <span class="badge badge-primary">Node.js</span>
                    <span class="badge badge-primary">Express</span>
                    <span class="badge badge-primary">PostgreSQL</span>
                    <span class="badge badge-primary">Prisma ORM</span>
                    <span class="badge badge-primary">Socket.IO</span>
                    <span class="badge badge-primary">JWT</span>
                    <span class="badge badge-primary">Google Gemini AI</span>
                </div>
            </section>

            <section id="authentication" class="section">
                <h2>Authentication</h2>
                <p>The API uses JWT (JSON Web Tokens) for authentication. To access protected endpoints, you need to include the JWT token in the Authorization header of your requests.</p>

                <h3>Authentication Header</h3>
                <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>

                <h3>User Roles</h3>
                <p>The API supports different user roles with varying permissions:</p>
                <ul>
                    <li><strong>User:</strong> Basic role with access to standard features</li>
                    <li><strong>Writer:</strong> Can create, edit, and delete their own posts</li>
                    <li><strong>Admin:</strong> Has full access to manage all content and users</li>
                </ul>
            </section>

            <section id="auth-endpoints" class="section">
                <h2>Authentication Endpoints</h2>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/register</span>
                    </div>
                    <div class="description">Register a new user account</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "username": "string",
  "email": "string",
  "password": "string"
}</code></pre>
                    <h4>Response:</h4>
                    <pre><code>{
  "message": "User registered successfully.",
  "status": 201
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/login</span>
                    </div>
                    <div class="description">Authenticate a user and get JWT token</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "email": "string",
  "password": "string"
}</code></pre>
                    <h4>Response:</h4>
                    <pre><code>{
  "token": "JWT_TOKEN",
  "status": 200
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/auth/userdata</span>
                    </div>
                    <div class="description">Get authenticated user's profile data</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Response:</h4>
                    <pre><code>{
  "id": "string",
  "username": "string",
  "email": "string",
  "role": "string",
  "profilePic": "string",
  "coverPic": "string",
  "followers": [...],
  "following": [...],
  "status": 200
}</code></pre>
                </div>

                <h3>Password Reset Flow</h3>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/forgot-password</span>
                    </div>
                    <div class="description">Initiate password reset and send OTP to user's email</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "email": "string"
}</code></pre>
                    <h4>Response:</h4>
                    <pre><code>{
  "message": "OTP sent to your email."
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/verify-otp</span>
                    </div>
                    <div class="description">Verify OTP and get reset token</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "email": "string",
  "otp": "string"
}</code></pre>
                    <h4>Response:</h4>
                    <pre><code>{
  "message": "OTP verified successfully.",
  "resetToken": "string"
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/reset-password</span>
                    </div>
                    <div class="description">Reset password using the reset token</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "resetToken": "string",
  "newPassword": "string"
}</code></pre>
                    <h4>Response:</h4>
                    <pre><code>{
  "message": "Password reset successful."
}</code></pre>
                </div>
            </section>

            <section id="blog-endpoints" class="section">
                <h2>Blog Posts Endpoints</h2>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/posts</span>
                    </div>
                    <div class="description">Get all blog posts with pagination</div>
                    <h4>Query Parameters:</h4>
                    <ul>
                        <li><code>limit</code> - Number of posts per page (default: 10)</li>
                        <li><code>page</code> - Page number (default: 1)</li>
                        <li><code>id</code> - Optional post ID to get a specific post</li>
                    </ul>
                    <h4>Response:</h4>
                    <p>Returns an array of post objects or a single post if ID is provided</p>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/posts</span>
                    </div>
                    <div class="description">Create a new blog post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Writer Role</span></p>
                    <h4>Request Body:</h4>
                    <p>Multipart form data with:</p>
                    <ul>
                        <li><code>Pdata</code> - JSON string containing post details:
                            <pre><code>{
  "title": "string",
  "content": "string",
  "category": "string"
}</code></pre>
                        </li>
                        <li><code>PostImg</code> - Image file (optional)</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method put">PUT</span>
                        <span class="url">/api/posts/:id</span>
                    </div>
                    <div class="description">Update a blog post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Writer Role</span> <span class="badge badge-danger">Post Owner</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                    <h4>Request Body:</h4>
                    <p>Multipart form data with:</p>
                    <ul>
                        <li><code>Pdata</code> - JSON string containing updated post details</li>
                        <li><code>PostImg</code> - New image file (optional)</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/posts/:id</span>
                    </div>
                    <div class="description">Delete a blog post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Writer Role</span> <span class="badge badge-danger">Post Owner</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/posts/summerize/:id</span>
                    </div>
                    <div class="description">Get AI-generated summary of a blog post using Google Gemini</div>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                    <h4>Response:</h4>
                    <pre><code>{
  "text": "AI-generated summary of the blog post"
}</code></pre>
                </div>

                <h3>Admin Post Management</h3>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method put">PUT</span>
                        <span class="url">/api/posts/admin/approve/:id</span>
                    </div>
                    <div class="description">Approve a blog post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method put">PUT</span>
                        <span class="url">/api/posts/admin/disapprove/:id</span>
                    </div>
                    <div class="description">Disapprove a blog post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                </div>
            </section>

            <section id="comment-endpoints" class="section">
                <h2>Comments Endpoints</h2>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/comments/:blogId</span>
                    </div>
                    <div class="description">Get all comments for a blog post</div>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>blogId</code> - Blog post ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/comments/:blogId</span>
                    </div>
                    <div class="description">Add a comment to a blog post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>blogId</code> - Blog post ID</li>
                    </ul>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "text": "string"
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/comments/reply/:commentId</span>
                    </div>
                    <div class="description">Reply to a comment</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>commentId</code> - Comment ID</li>
                    </ul>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "text": "string"
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method put">PUT</span>
                        <span class="url">/api/comments/:id</span>
                    </div>
                    <div class="description">Edit a comment</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-danger">Comment Owner</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Comment ID</li>
                    </ul>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "text": "string"
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/comments/:id</span>
                    </div>
                    <div class="description">Delete a comment</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-danger">Comment Owner</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Comment ID</li>
                    </ul>
                </div>

                <h3>Admin Comment Management</h3>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method put">PUT</span>
                        <span class="url">/api/comments/:id/approve</span>
                    </div>
                    <div class="description">Approve a comment</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Comment ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method put">PUT</span>
                        <span class="url">/api/comments/:id/disapprove</span>
                    </div>
                    <div class="description">Disapprove a comment</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Comment ID</li>
                    </ul>
                </div>
            </section>

            <section id="category-endpoints" class="section">
                <h2>Categories Endpoints</h2>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/categories</span>
                    </div>
                    <div class="description">Get all categories</div>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/categories/:id</span>
                    </div>
                    <div class="description">Create a new category</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Category ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/categories/:id</span>
                    </div>
                    <div class="description">Delete a category</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Category ID</li>
                    </ul>
                </div>
            </section>

            <section id="challenge-endpoints" class="section">
                <h2>Challenge System Endpoints</h2>
                <p>The challenge system allows users to challenge or dispute content in blog posts.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/challenges/post/:id</span>
                    </div>
                    <div class="description">Create a new challenge for a post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "content": "string" // Challenge description/reason
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/challenges/post/:id</span>
                    </div>
                    <div class="description">Get all challenges for a post</div>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Post ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method patch">PATCH</span>
                        <span class="url">/api/challenges/:id</span>
                    </div>
                    <div class="description">Update challenge status</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Challenge ID</li>
                    </ul>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "status": "string" // "pending", "accepted", or "rejected"
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/challenges/:id</span>
                    </div>
                    <div class="description">Delete a challenge</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>id</code> - Challenge ID</li>
                    </ul>
                </div>
            </section>

            <section id="chat-endpoints" class="section">
                <h2>Chat System Endpoints</h2>
                <p>The chat system provides real-time messaging between users using Socket.IO.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/chat</span>
                    </div>
                    <div class="description">Get chat history between two users</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "from": "string", // Sender user ID
  "to": "string"    // Recipient user ID
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/chat</span>
                    </div>
                    <div class="description">Get all chats for a user</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "from": "string" // User ID
}</code></pre>
                </div>

                <h3>Socket.IO Events</h3>
                <p>The chat system uses Socket.IO for real-time communication. Here are the key events:</p>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Event</th>
                                <th>Direction</th>
                                <th>Data</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>join</code></td>
                                <td>Client → Server</td>
                                <td><code>"userId"</code></td>
                                <td>Register a user's socket connection</td>
                            </tr>
                            <tr>
                                <td><code>sendMessage</code></td>
                                <td>Client → Server</td>
                                <td><code>{ from, to, content }</code></td>
                                <td>Send a message to another user</td>
                            </tr>
                            <tr>
                                <td><code>receivedMessage</code></td>
                                <td>Server → Client</td>
                                <td><code>{ from, to, content, timestamp }</code></td>
                                <td>Receive a message from another user</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section id="follow-endpoints" class="section">
                <h2>Follow System Endpoints</h2>
                <p>The follow system allows users to follow and unfollow other users.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/follow/:followingId</span>
                    </div>
                    <div class="description">Follow a user</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>followingId</code> - ID of the user to follow</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/follow</span>
                    </div>
                    <div class="description">Unfollow a user</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "followerId": "string", // ID of the follower (current user)
  "followingId": "string" // ID of the user being followed
}</code></pre>
                </div>
            </section>

            <section id="vote-endpoints" class="section">
                <h2>Voting Endpoints</h2>
                <p>The voting system allows users to upvote or downvote posts.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/vote/upvote</span>
                    </div>
                    <div class="description">Upvote a post</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "postId": "string" // ID of the post to upvote
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/vote/downvote</span>
                    </div>
                    <div class="description">Downvote a post</div>
                    <h4>Request Body:</h4>
                    <pre><code>{
  "postId": "string" // ID of the post to downvote
}</code></pre>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/vote/votes/:postId</span>
                    </div>
                    <div class="description">Get all votes for a post</div>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>postId</code> - Post ID</li>
                    </ul>
                </div>
            </section>

            <section id="love-endpoints" class="section">
                <h2>Post Love Endpoints</h2>
                <p>The post love system allows users to add love reactions to posts.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method post">POST</span>
                        <span class="url">/api/post-love/:postId</span>
                    </div>
                    <div class="description">Add love reaction to a post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>postId</code> - Post ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/post-love/:postId</span>
                    </div>
                    <div class="description">Remove love reaction from a post</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>postId</code> - Post ID</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/post-love/:postId/count</span>
                    </div>
                    <div class="description">Get love reactions count for a post</div>
                    <h4>Parameters:</h4>
                    <ul>
                        <li><code>postId</code> - Post ID</li>
                    </ul>
                </div>
            </section>

            <section id="notification-endpoints" class="section">
                <h2>Notification Endpoints</h2>
                <p>The notification system keeps users informed about activities related to their content.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/notifications</span>
                    </div>
                    <div class="description">Get user notifications with pagination</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Query Parameters:</h4>
                    <ul>
                        <li><code>page</code> - Page number (default: 1)</li>
                        <li><code>limit</code> - Items per page (default: 10)</li>
                    </ul>
                </div>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/notifications/summary</span>
                    </div>
                    <div class="description">Get AI-powered summary of unread notifications</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span></p>
                    <h4>Response:</h4>
                    <pre><code>{
  "summary": "AI-generated summary of unread notifications",
  "unreadCount": 5
}</code></pre>
                </div>
            </section>

            <section id="activity-endpoints" class="section">
                <h2>Activity Logs Endpoints</h2>
                <p>The activity logging system tracks admin actions for auditing purposes.</p>

                <div class="endpoint">
                    <div class="endpoint-title">
                        <span class="method get">GET</span>
                        <span class="url">/api/activity</span>
                    </div>
                    <div class="description">Get activity logs with pagination (admin only)</div>
                    <h4>Authentication:</h4>
                    <p><span class="badge badge-warning">Required</span> <span class="badge badge-secondary">Admin Role</span></p>
                </div>
            </section>

            <section id="realtime" class="section">
                <h2>Real-time Features</h2>
                <p>The API includes Socket.IO integration for real-time features:</p>

                <h3>Chat System</h3>
                <p>Real-time messaging between users with instant delivery and notifications.</p>

                <h3>Post Updates</h3>
                <p>When a post is updated, all connected clients viewing that post receive real-time updates.</p>

                <h3>Notifications</h3>
                <p>Users receive instant notifications for activities related to their content.</p>

                <h3>Socket.IO Connection</h3>
                <pre><code>// Connect to Socket.IO server
const socket = io('https://pothoczuto-news-media.onrender.com', {
  withCredentials: true,
  transports: ['websocket', 'polling']
});

// Join as a user
socket.emit('join', userId);

// Listen for messages
socket.on('receivedMessage', (data) => {
  console.log('New message:', data);
});

// Send a message
socket.emit('sendMessage', {
  from: 'user123',
  to: 'user456',
  content: 'Hello, this is a test message!'
});</code></pre>
            </section>

            <section id="ai-integration" class="section">
                <h2>AI Integration</h2>
                <p>The API integrates with Google's Gemini AI for enhanced features:</p>

                <h3>Blog Post Summarization</h3>
                <p>Automatically generate concise summaries of blog posts using AI.</p>
                <p>Endpoint: <code>GET /api/posts/summerize/:id</code></p>

                <h3>Notification Summaries</h3>
                <p>Get AI-powered summaries of unread notifications to quickly catch up on missed activities.</p>
                <p>Endpoint: <code>GET /api/notifications/summary</code></p>
            </section>

            <section id="error-handling" class="section">
                <h2>Error Handling</h2>
                <p>The API uses standard HTTP status codes and consistent error response format:</p>

                <h3>Status Codes</h3>
                <ul>
                    <li><strong>200</strong> - Success</li>
                    <li><strong>201</strong> - Created</li>
                    <li><strong>400</strong> - Bad Request</li>
                    <li><strong>401</strong> - Unauthorized</li>
                    <li><strong>403</strong> - Forbidden</li>
                    <li><strong>404</strong> - Not Found</li>
                    <li><strong>500</strong> - Internal Server Error</li>
                </ul>

                <h3>Error Response Format</h3>
                <pre><code>{
  "message": "Error description",
  "status": 400
}</code></pre>

                <h3>Validation Errors</h3>
                <p>Input validation is performed using Joi. Validation errors return a 400 status code with details about the validation failure.</p>
                <pre><code>{
  "message": "\"email\" must be a valid email",
  "status": 400
}</code></pre>
            </section>

            <section id="frontend-integration" class="section">
                <h2>Frontend Integration Guide</h2>
                <p>This comprehensive guide provides detailed instructions on how to integrate all backend systems with your frontend application.</p>

                <h3>Authentication System</h3>

                <h4>User Registration</h4>
                <ol>
                    <li>
                        <strong>Registration Form</strong>
                        <ul>
                            <li>Create a form with username, email, and password fields</li>
                            <li>Implement client-side validation (password length, email format)</li>
                            <li>On submit, call <code>POST /api/auth/register</code> with form data</li>
                            <li>Display success message and redirect to login page</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example registration request
const registerUser = async (userData) => {
  try {
    const response = await fetch('https://pothoczuto-news-media.onrender.com/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};</code></pre>

                <h4>Login Flow</h4>
                <ol>
                    <li>
                        <strong>Login Form</strong>
                        <ul>
                            <li>Create a form with email and password fields</li>
                            <li>On submit, call <code>POST /api/auth/login</code></li>
                            <li>Store the returned JWT token in localStorage or secure cookie</li>
                            <li>Redirect to dashboard or home page</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Auth State Management</strong>
                        <ul>
                            <li>Create an auth context/store to manage authentication state</li>
                            <li>Check for token on app initialization</li>
                            <li>Implement auto-logout on token expiration</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example login request and token storage
const loginUser = async (credentials) => {
  const response = await fetch('https://pothoczuto-news-media.onrender.com/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials)
  });
  const data = await response.json();

  if (data.token) {
    localStorage.setItem('authToken', data.token);
    return true;
  }
  return false;
};</code></pre>

                <h4>Password Reset Flow</h4>
                <ol>
                    <li>
                        <strong>Forgot Password Screen</strong>
                        <ul>
                            <li>User enters their email address</li>
                            <li>Frontend calls <code>POST /api/auth/forgot-password</code> with the email</li>
                            <li>Backend sends OTP to the user's email</li>
                            <li>Frontend shows message to check email for OTP</li>
                        </ul>
                    </li>
                    <li>
                        <strong>OTP Verification Screen</strong>
                        <ul>
                            <li>User enters the OTP received in their email</li>
                            <li>Frontend calls <code>POST /api/auth/verify-otp</code> with email and OTP</li>
                            <li>Backend verifies OTP and returns a reset token</li>
                            <li>Frontend stores the reset token temporarily</li>
                        </ul>
                    </li>
                    <li>
                        <strong>New Password Screen</strong>
                        <ul>
                            <li>User enters new password</li>
                            <li>Frontend calls <code>POST /api/auth/reset-password</code> with reset token and new password</li>
                            <li>Backend updates the password</li>
                            <li>Frontend redirects to login page with success message</li>
                        </ul>
                    </li>
                </ol>

                <h4>Protected Routes</h4>
                <p>Implement route protection in your frontend framework:</p>
                <pre><code>// Example React protected route component
const ProtectedRoute = ({ children }) => {
  const token = localStorage.getItem('authToken');

  if (!token) {
    // Redirect to login page
    return <Navigate to="/login" />;
  }

  return children;
};</code></pre>

                <h3>Blog Post System</h3>

                <h4>Displaying Posts</h4>
                <ol>
                    <li>
                        <strong>Post List Page</strong>
                        <ul>
                            <li>Fetch posts with <code>GET /api/posts?page=1&limit=10</code></li>
                            <li>Implement pagination controls</li>
                            <li>Display post cards with title, excerpt, author, and thumbnail</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Single Post Page</strong>
                        <ul>
                            <li>Fetch post details with <code>GET /api/posts?id=postId</code></li>
                            <li>Display full post content with author info</li>
                            <li>Show comments section below the post</li>
                            <li>Add AI summary button that calls <code>GET /api/posts/summerize/:id</code></li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example post fetching with pagination
const fetchPosts = async (page = 1, limit = 10) => {
  const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/posts?page=${page}&limit=${limit}`);
  return await response.json();
};</code></pre>

                <h4>Creating and Editing Posts</h4>
                <ol>
                    <li>
                        <strong>Post Editor</strong>
                        <ul>
                            <li>Implement a rich text editor (e.g., Quill, TinyMCE)</li>
                            <li>Add image upload functionality</li>
                            <li>Include category selection dropdown</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Form Submission</strong>
                        <ul>
                            <li>For new posts: <code>POST /api/posts</code> with FormData</li>
                            <li>For editing: <code>PUT /api/posts/:id</code> with FormData</li>
                            <li>Handle image uploads properly in FormData</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example post creation with image upload
const createPost = async (postData, image) => {
  const formData = new FormData();
  const jsonData = JSON.stringify({
    title: postData.title,
    content: postData.content,
    category: postData.category
  });

  formData.append('Pdata', jsonData);
  if (image) {
    formData.append('PostImg', image);
  }

  const response = await fetch('https://pothoczuto-news-media.onrender.com/api/posts', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    },
    body: formData
  });

  return await response.json();
};</code></pre>

                <h3>Comment System</h3>

                <h4>Displaying Comments</h4>
                <ol>
                    <li>
                        <strong>Comments Section</strong>
                        <ul>
                            <li>Fetch comments with <code>GET /api/comments/:blogId</code></li>
                            <li>Display comments in a threaded view</li>
                            <li>Show reply buttons for each comment</li>
                        </ul>
                    </li>
                </ol>

                <h4>Adding Comments and Replies</h4>
                <ol>
                    <li>
                        <strong>Comment Form</strong>
                        <ul>
                            <li>Create a simple text input for comments</li>
                            <li>Submit new comments with <code>POST /api/comments/:blogId</code></li>
                        </ul>
                    </li>
                    <li>
                        <strong>Reply Form</strong>
                        <ul>
                            <li>Show reply form when reply button is clicked</li>
                            <li>Submit replies with <code>POST /api/comments/reply/:commentId</code></li>
                            <li>Update UI to show new reply immediately</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example comment submission
const addComment = async (blogId, text) => {
  const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/comments/${blogId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    },
    body: JSON.stringify({ text })
  });

  return await response.json();
};</code></pre>

                <h3>Real-time Chat System</h3>
                <p>The Chat System provides real-time messaging between users using Socket.IO. This guide covers setting up the chat interface and handling real-time communication.</p>

                <h4>Setting Up Socket.IO</h4>
                <ol>
                    <li>
                        <strong>Installation and Configuration</strong>
                        <ul>
                            <li>Install Socket.IO client: <code>npm install socket.io-client</code></li>
                            <li>Create a socket service/utility for managing connections</li>
                            <li>Initialize connection when user logs in</li>
                            <li>Handle reconnection logic for network interruptions</li>
                        </ul>
                    </li>
                    <li>
                        <strong>User Registration</strong>
                        <ul>
                            <li>Register user with <code>socket.emit('join', userId)</code></li>
                            <li>Set up event listeners for various socket events</li>
                            <li>Implement connection status indicators in the UI</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example Socket.IO setup
import { io } from 'socket.io-client';

class ChatService {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.messageHandlers = [];
  }

  connect(userId) {
    this.socket = io('https://pothoczuto-news-media.onrender.com', {
      withCredentials: true,
      transports: ['websocket', 'polling']
    });

    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.connected = true;
      this.socket.emit('join', userId);
    });

    this.socket.on('disconnect', () => {
      console.log('Socket disconnected');
      this.connected = false;
    });

    this.socket.on('receivedMessage', (data) => {
      this.messageHandlers.forEach(handler => handler(data));
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.connected = false;
    }
  }

  onMessage(handler) {
    this.messageHandlers.push(handler);
    return () => {
      this.messageHandlers = this.messageHandlers.filter(h => h !== handler);
    };
  }

  sendMessage(from, to, content) {
    if (!this.connected) {
      throw new Error('Socket not connected');
    }
    this.socket.emit('sendMessage', { from, to, content });
  }
}

// Create a singleton instance
export const chatService = new ChatService();</code></pre>

                <h4>Chat Interface</h4>
                <ol>
                    <li>
                        <strong>Chat List Component</strong>
                        <ul>
                            <li>Fetch user's chats with <code>GET /api/chat</code> (body: <code>{ from: userId }</code>)</li>
                            <li>Display list of conversations with other users</li>
                            <li>Show unread message count for each conversation</li>
                            <li>Display last message preview and timestamp</li>
                            <li>Implement search functionality for finding conversations</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Chat Window Component</strong>
                        <ul>
                            <li>Fetch chat history with <code>GET /api/chat</code> (body: <code>{ from: userId, to: recipientId }</code>)</li>
                            <li>Display messages in chronological order with timestamps</li>
                            <li>Show read/delivered status indicators</li>
                            <li>Implement infinite scroll for loading older messages</li>
                            <li>Add message input form with emoji picker and attachment options</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Real-time Message Handling</strong>
                        <ul>
                            <li>Send messages with <code>chatService.sendMessage(from, to, content)</code></li>
                            <li>Listen for incoming messages with <code>chatService.onMessage(callback)</code></li>
                            <li>Update UI in real-time when messages are received</li>
                            <li>Implement typing indicators with <code>socket.emit('typing', { from, to })</code></li>
                            <li>Handle message delivery and read receipts</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example: Chat List Component
const ChatList = ({ userId }) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        const response = await fetch('https://pothoczuto-news-media.onrender.com/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify({ from: userId })
        });

        const data = await response.json();
        setConversations(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching conversations:', error);
        setLoading(false);
      }
    };

    fetchConversations();

    // Listen for new messages to update conversation list
    const unsubscribe = chatService.onMessage((message) => {
      setConversations(prev => {
        // Update conversation with new message
        return prev.map(conv => {
          if (conv.userId === message.from || conv.userId === message.to) {
            return {
              ...conv,
              lastMessage: message.content,
              timestamp: new Date().toISOString(),
              unread: conv.userId === message.from ? conv.unread + 1 : conv.unread
            };
          }
          return conv;
        });
      });
    });

    return () => unsubscribe();
  }, [userId]);

  return (
    <div className="chat-list">
      {loading ? (
        <div>Loading conversations...</div>
      ) : (
        conversations.map(conv => (
          <div key={conv.userId} className="conversation-item">
            <div className="avatar">{/* User avatar */}</div>
            <div className="conversation-details">
              <div className="conversation-header">
                <span className="username">{conv.username}</span>
                <span className="timestamp">{formatTime(conv.timestamp)}</span>
              </div>
              <div className="last-message">{conv.lastMessage}</div>
              {conv.unread > 0 && <div className="unread-badge">{conv.unread}</div>}
            </div>
          </div>
        ))
      )}
    </div>
  );
};

// Example: Sending and receiving messages
const ChatWindow = ({ userId, recipientId }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const response = await fetch('https://pothoczuto-news-media.onrender.com/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify({ from: userId, to: recipientId })
        });

        const data = await response.json();
        setMessages(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching messages:', error);
        setLoading(false);
      }
    };

    fetchMessages();

    // Listen for new messages
    const unsubscribe = chatService.onMessage((message) => {
      if (message.from === recipientId || message.to === recipientId) {
        setMessages(prev => [...prev, message]);
      }
    });

    return () => unsubscribe();
  }, [userId, recipientId]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    try {
      chatService.sendMessage(userId, recipientId, newMessage);

      // Optimistically add message to UI
      const newMsg = {
        from: userId,
        to: recipientId,
        content: newMessage,
        timestamp: new Date().toISOString(),
        status: 'sent'
      };

      setMessages(prev => [...prev, newMsg]);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    }
  };

  return (
    <div className="chat-window">
      <div className="message-list">
        {loading ? (
          <div>Loading messages...</div>
        ) : (
          messages.map((msg, index) => (
            <div
              key={index}
              className={`message ${msg.from === userId ? 'sent' : 'received'}`}
            >
              <div className="message-content">{msg.content}</div>
              <div className="message-meta">
                <span className="timestamp">{formatTime(msg.timestamp)}</span>
                {msg.from === userId && (
                  <span className="status">{msg.status}</span>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      <div className="message-input">
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a message..."
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        />
        <button onClick={handleSendMessage}>Send</button>
      </div>
    </div>
  );
};</code></pre>

                <h3>Follow System</h3>

                <h4>User Profile and Following</h4>
                <ol>
                    <li>
                        <strong>User Profile Page</strong>
                        <ul>
                            <li>Display user information and posts</li>
                            <li>Show follow/unfollow button</li>
                            <li>Display follower and following counts</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Follow Actions</strong>
                        <ul>
                            <li>Follow a user: <code>POST /api/follow/:followingId</code></li>
                            <li>Unfollow a user: <code>DELETE /api/follow</code> with appropriate body</li>
                            <li>Update UI immediately after follow/unfollow action</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example follow/unfollow functionality
const followUser = async (followingId) => {
  const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/follow/${followingId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    }
  });

  return await response.json();
};

const unfollowUser = async (followerId, followingId) => {
  const response = await fetch('https://pothoczuto-news-media.onrender.com/api/follow', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    },
    body: JSON.stringify({ followerId, followingId })
  });

  return await response.json();
};</code></pre>

                <h3>Post Interaction Systems</h3>

                <h4>Voting System</h4>
                <p>The Voting System allows users to upvote or downvote posts, providing a way to rank content by popularity.</p>
                <ol>
                    <li>
                        <strong>Vote Buttons UI</strong>
                        <ul>
                            <li>Add upvote/downvote buttons to posts with appropriate icons</li>
                            <li>Show current vote count next to buttons</li>
                            <li>Highlight the button that represents the user's current vote</li>
                            <li>Implement hover effects and animations for better UX</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Vote Actions</strong>
                        <ul>
                            <li>Upvote: <code>POST /api/vote/upvote</code> with post ID</li>
                            <li>Downvote: <code>POST /api/vote/downvote</code> with post ID</li>
                            <li>Remove vote: Send the same request as the current vote to toggle it off</li>
                            <li>Update vote count in UI immediately after voting (optimistic updates)</li>
                            <li>Handle error cases and revert UI if the API request fails</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Vote Analytics</strong>
                        <ul>
                            <li>Display vote ratio (upvotes vs. downvotes) as a percentage</li>
                            <li>Show trending posts based on recent vote activity</li>
                            <li>Implement sorting options based on vote count or ratio</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example: Vote functionality with optimistic updates
const VoteButtons = ({ postId, initialVotes }) => {
  const [voteStatus, setVoteStatus] = useState(null); // null, 'upvote', or 'downvote'
  const [voteCount, setVoteCount] = useState(initialVotes.count);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if user has already voted on this post
    const checkUserVote = async () => {
      try {
        const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/vote/status/${postId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        });

        const data = await response.json();
        setVoteStatus(data.status); // Will be null, 'upvote', or 'downvote'
      } catch (error) {
        console.error('Error checking vote status:', error);
      }
    };

    checkUserVote();
  }, [postId]);

  const handleVote = async (voteType) => {
    if (isLoading) return;

    // If user clicks the same vote type again, they're toggling it off
    const isRemovingVote = voteStatus === voteType;

    // Optimistically update UI
    const previousVoteStatus = voteStatus;
    const previousVoteCount = voteCount;

    // Update local state optimistically
    if (isRemovingVote) {
      setVoteStatus(null);
      setVoteCount(voteCount - (voteType === 'upvote' ? 1 : -1));
    } else {
      // If changing vote from down to up or vice versa, need to adjust by 2
      const adjustment = voteStatus ? 2 : 1;
      setVoteStatus(voteType);
      setVoteCount(voteCount + (voteType === 'upvote' ? adjustment : -adjustment));
    }

    setIsLoading(true);

    try {
      const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/vote/${voteType}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({ postId })
      });

      if (!response.ok) {
        throw new Error('Vote request failed');
      }

      const data = await response.json();
      // Update with actual server data
      setVoteCount(data.voteCount);
      setIsLoading(false);
    } catch (error) {
      // Revert to previous state if request fails
      setVoteStatus(previousVoteStatus);
      setVoteCount(previousVoteCount);
      setIsLoading(false);
      console.error('Error voting:', error);
      alert('Failed to register vote. Please try again.');
    }
  };

  return (
    <div className="vote-buttons">
      <button
        className={`upvote-button ${voteStatus === 'upvote' ? 'active' : ''}`}
        onClick="handleVote('upvote')"
        disabled={isLoading}
      >
        <span className="vote-icon">▲</span>
      </button>

      <span className="vote-count">{voteCount}</span>

      <button
        className={`downvote-button ${voteStatus === 'downvote' ? 'active' : ''}`}
        onClick="handleVote('downvote')"
        disabled={isLoading}
      >
        <span className="vote-icon">▼</span>
      </button>
    </div>
  );
};</code></pre>

                <h4>Love Reactions</h4>
                <ol>
                    <li>
                        <strong>Love Button</strong>
                        <ul>
                            <li>Add heart/love button to posts</li>
                            <li>Add love: <code>POST /api/post-love/:postId</code></li>
                            <li>Remove love: <code>DELETE /api/post-love/:postId</code></li>
                            <li>Get love count: <code>GET /api/post-love/:postId/count</code></li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example love reaction toggle
const toggleLoveReaction = async (postId, currentlyLoved) => {
  const url = `https://pothoczuto-news-media.onrender.com/api/post-love/${postId}`;
  const method = currentlyLoved ? 'DELETE' : 'POST';

  const response = await fetch(url, {
    method,
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    }
  });

  return await response.json();
};</code></pre>

                <h3>Challenge System</h3>
                <p>The Challenge System allows users to dispute or question content in blog posts. This guide covers implementation of challenge creation, listing, and management features.</p>

                <h4>Creating and Managing Challenges</h4>
                <ol>
                    <li>
                        <strong>Challenge Button</strong>
                        <ul>
                            <li>Add "Challenge Content" button to posts</li>
                            <li>Show challenge form with reason input</li>
                            <li>Submit challenge: <code>POST /api/challenges/post/:id</code></li>
                            <li>Display success message after submission</li>
                            <li>Update UI to show that the post has been challenged</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Challenge List</strong>
                        <ul>
                            <li>Fetch challenges for a post: <code>GET /api/challenges/post/:id</code></li>
                            <li>Display challenges with status indicators (pending, accepted, rejected)</li>
                            <li>Show challenge content and author information</li>
                            <li>Add timestamp and status history if available</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Challenge Management</strong>
                        <ul>
                            <li>For admins: Show list of all challenges across posts</li>
                            <li>Implement filtering by status, date, and post</li>
                            <li>Update challenge status: <code>PATCH /api/challenges/:id</code></li>
                            <li>Delete challenge: <code>DELETE /api/challenges/:id</code></li>
                            <li>Add admin response field for challenge resolution</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example: Creating a challenge
const createChallenge = async (postId, content) => {
  try {
    const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/challenges/post/${postId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify({ content })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating challenge:', error);
    throw error;
  }
};

// Example: Fetching challenges for a post
const getChallenges = async (postId) => {
  try {
    const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/challenges/post/${postId}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching challenges:', error);
    throw error;
  }
};

// Example: Updating challenge status (admin)
const updateChallengeStatus = async (challengeId, status) => {
  try {
    const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/challenges/${challengeId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify({ status }) // status can be "pending", "accepted", or "rejected"
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating challenge status:', error);
    throw error;
  }
};</code></pre>

                <h3>Notification System</h3>

                <h4>Displaying Notifications</h4>
                <ol>
                    <li>
                        <strong>Notification Bell</strong>
                        <ul>
                            <li>Add notification icon in header with unread count</li>
                            <li>Fetch notifications: <code>GET /api/notifications</code></li>
                            <li>Show notification dropdown with recent items</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Notification Page</strong>
                        <ul>
                            <li>Display all notifications with pagination</li>
                            <li>Add "AI Summary" button that calls <code>GET /api/notifications/summary</code></li>
                            <li>Show AI-generated summary of recent notifications</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example notification fetching
const fetchNotifications = async (page = 1, limit = 10) => {
  const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/notifications?page=${page}&limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    }
  });

  return await response.json();
};

// Get AI summary of notifications
const getNotificationSummary = async () => {
  const response = await fetch('https://pothoczuto-news-media.onrender.com/api/notifications/summary', {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    }
  });

  return await response.json();
};</code></pre>

                <h3>Admin Dashboard</h3>

                <h4>Content Moderation</h4>
                <ol>
                    <li>
                        <strong>Post Approval</strong>
                        <ul>
                            <li>List pending posts</li>
                            <li>Approve post: <code>PUT /api/posts/admin/approve/:id</code></li>
                            <li>Disapprove post: <code>PUT /api/posts/admin/disapprove/:id</code></li>
                        </ul>
                    </li>
                    <li>
                        <strong>Comment Moderation</strong>
                        <ul>
                            <li>List reported or pending comments</li>
                            <li>Approve comment: <code>PUT /api/comments/:id/approve</code></li>
                            <li>Disapprove comment: <code>PUT /api/comments/:id/disapprove</code></li>
                        </ul>
                    </li>
                </ol>

                <h4>Activity Logs</h4>
                <p>The Activity Logging system tracks admin actions for auditing and monitoring purposes.</p>
                <ol>
                    <li>
                        <strong>Admin Activity Dashboard</strong>
                        <ul>
                            <li>Create an admin-only activity logs dashboard</li>
                            <li>Fetch activity logs: <code>GET /api/activity</code></li>
                            <li>Implement pagination for large log volumes</li>
                            <li>Display logs with timestamps, user info, and action details</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Filtering and Searching</strong>
                        <ul>
                            <li>Add filters for action types (create, update, delete, etc.)</li>
                            <li>Implement date range filtering</li>
                            <li>Add user filtering to see actions by specific admins</li>
                            <li>Include search functionality for finding specific log entries</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Log Visualization</strong>
                        <ul>
                            <li>Create charts and graphs to visualize admin activity</li>
                            <li>Show activity trends over time</li>
                            <li>Highlight unusual patterns or potential security issues</li>
                        </ul>
                    </li>
                </ol>
                <pre><code>// Example: Fetching activity logs with filtering
const fetchActivityLogs = async (page = 1, limit = 20, filters = {}) => {
  try {
    // Build query string from filters
    const queryParams = new URLSearchParams({
      page,
      limit,
      ...filters // Can include: actionType, startDate, endDate, userId
    }).toString();

    const response = await fetch(`https://pothoczuto-news-media.onrender.com/api/activity?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      }
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    throw error;
  }
};

// Example: Activity Logs Dashboard Component
const ActivityLogsDashboard = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({});
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    const loadLogs = async () => {
      setLoading(true);
      try {
        const data = await fetchActivityLogs(page, 20, filters);
        setLogs(data.logs);
        setTotalPages(data.totalPages);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error('Failed to load activity logs', error);
      }
    };

    loadLogs();
  }, [page, filters]);

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  };

  return (
    <div className="activity-logs-dashboard">
      <h2>Admin Activity Logs</h2>

      <div className="filters">
        <select
          onChange={(e) => handleFilterChange({...filters, actionType: e.target.value})}
          value={filters.actionType || ''}
        >
          <option value="">All Actions</option>
          <option value="create">Create</option>
          <option value="update">Update</option>
          <option value="delete">Delete</option>
          <option value="approve">Approve</option>
          <option value="reject">Reject</option>
        </select>

        <div className="date-filters">
          <label>
            From:
            <input
              type="date"
              onChange={(e) => handleFilterChange({...filters, startDate: e.target.value})}
              value={filters.startDate || ''}
            />
          </label>

          <label>
            To:
            <input
              type="date"
              onChange={(e) => handleFilterChange({...filters, endDate: e.target.value})}
              value={filters.endDate || ''}
            />
          </label>
        </div>
      </div>

      {loading ? (
        <div>Loading activity logs...</div>
      ) : (
        <>
          <table className="logs-table">
            <thead>
              <tr>
                <th>Timestamp</th>
                <th>Admin</th>
                <th>Action</th>
                <th>Target</th>
                <th>Details</th>
              </tr>
            </thead>
            <tbody>
              {logs.map(log => (
                <tr key={log.id}>
                  <td>{new Date(log.timestamp).toLocaleString()}</td>
                  <td>{log.adminName}</td>
                  <td>{log.actionType}</td>
                  <td>{log.targetType} #{log.targetId}</td>
                  <td>{log.details}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="pagination">
            <button
              disabled={page === 1}
              onClick="setPage(Math.max(1, page - 1))"
            >
              Previous
            </button>

            <span>Page {page} of {totalPages}</span>

            <button
              disabled={page === totalPages}
              onClick="setPage(Math.min(totalPages, page + 1))"
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  );
};</code></pre>

                <h3>Category Management</h3>

                <h4>Category CRUD</h4>
                <ol>
                    <li>
                        <strong>Category List</strong>
                        <ul>
                            <li>Fetch categories: <code>GET /api/categories</code></li>
                            <li>Create category: <code>POST /api/categories/:id</code></li>
                            <li>Delete category: <code>DELETE /api/categories/:id</code></li>
                        </ul>
                    </li>
                </ol>

                <div class="footer">
                    <p>© 2023 Pothoczuto News Media API Documentation</p>
                </div>
            </section>
        </div>
    </div>
</body>
</html>

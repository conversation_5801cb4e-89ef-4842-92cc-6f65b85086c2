{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/contexts/ProfileContext.js"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useState, useEffect } from 'react';\n\n// Sample user data for development\nconst sampleUserData = {\n  id: '1',\n  username: '<PERSON>',\n  handle: '@sarahjournalist',\n  bio: 'Award-winning investigative journalist. Covering tech, politics, and social issues. Author of \"Digital Frontiers\" and host of \"The Inside Scoop\" podcast. Always searching for the truth.',\n  profilePic: 'https://images.unsplash.com/photo-1511367461989-f85a21fda167?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&q=80&w=1080',\n  coverPic: 'https://images.unsplash.com/photo-1517999144091-3d9dca6d1e43?auto=format&fit=crop&q=80&w=1200',\n  company: 'TechNews Media',\n  location: 'San Francisco, CA',\n  website: 'sarahjohnson.media',\n  joinDate: 'June 2019',\n  verified: true,\n  stats: {\n    posts: 582,\n    followers: 14200,\n    following: 523,\n    challenges: 36,\n    loves: 95300\n  },\n  followers: [\n    {\n      id: '101',\n      username: '<PERSON>',\n      profilePic: 'https://images.unsplash.com/photo-1625745184151-29d753a09781?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&q=80&w=1080'\n    },\n    {\n      id: '102',\n      username: 'John Smith',\n      profilePic: 'https://images.unsplash.com/photo-1541844053589-346841d0b34c?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&q=80&w=1080'\n    },\n    {\n      id: '103',\n      username: 'Alex Chen',\n      profilePic: 'https://images.unsplash.com/photo-1589644987356-c5c1d8e045e5?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&q=80&w=1080'\n    }\n  ]\n};\n\n// Sample posts data\nconst samplePosts = [\n  {\n    id: '1',\n    image: 'https://images.unsplash.com/photo-1581905764498-f1b60bae941a?auto=format&fit=crop&q=80&w=400',\n    alt: 'Tech article thumbnail',\n    category: 'Technology',\n    date: 'June 15, 2023',\n    title: 'The Future of AI in Newsrooms: Revolutionizing Journalism',\n    description: 'Exploring how artificial intelligence is transforming the way journalists research, write, and distribute news content in the digital age.',\n    likes: 345,\n    comments: 42,\n    isVideo: false,\n  },\n  {\n    id: '2',\n    image: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?auto=format&fit=crop&q=80&w=400',\n    alt: 'Politics article thumbnail',\n    category: 'Politics',\n    date: 'May 28, 2023',\n    title: 'Understanding the New Voting Rights Legislation',\n    description: 'A deep dive into the latest voting rights bill and what it means for citizens across the country.',\n    likes: 289,\n    comments: 56,\n    isVideo: false,\n  },\n  {\n    id: '3',\n    image: 'https://images.unsplash.com/photo-1576267423445-b2e0074d68a4?auto=format&fit=crop&q=80&w=400',\n    alt: 'Social issues article thumbnail',\n    category: 'Social Issues',\n    date: 'April 10, 2023',\n    title: 'The Housing Crisis: Finding Solutions in Urban Areas',\n    description: 'Examining innovative approaches to affordable housing in major cities facing severe housing shortages.',\n    likes: 412,\n    comments: 38,\n    isVideo: false,\n  },\n  {\n    id: '4',\n    image: '',\n    alt: 'Video interview thumbnail',\n    category: 'Interviews',\n    date: 'March 5, 2023',\n    title: 'Exclusive Interview: Tech CEO on Privacy Concerns',\n    description: 'Watch our in-depth conversation with a leading tech executive about data privacy in the digital age.',\n    likes: 523,\n    comments: 67,\n    isVideo: true,\n  },\n  {\n    id: '5',\n    image: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?auto=format&fit=crop&q=80&w=400',\n    alt: 'Business article thumbnail',\n    category: 'Business',\n    date: 'February 22, 2023',\n    title: 'Startup Funding Reaches Record Highs Despite Economic Uncertainty',\n    description: 'Analysis of the venture capital landscape and why investors are still betting big on tech startups.',\n    likes: 278,\n    comments: 31,\n    isVideo: false,\n  },\n  {\n    id: '6',\n    image: 'https://images.unsplash.com/photo-1532938911079-1b06ac7ceec7?auto=format&fit=crop&q=80&w=400',\n    alt: 'Health article thumbnail',\n    category: 'Health',\n    date: 'January 15, 2023',\n    title: 'New Research Reveals Link Between Sleep and Mental Health',\n    description: 'Scientists discover more evidence connecting quality sleep patterns to improved psychological wellbeing.',\n    likes: 367,\n    comments: 45,\n    isVideo: false,\n  }\n];\n\n// Create the context\nconst ProfileContext = createContext();\n\nexport function ProfileProvider({ children }) {\n  // User data state\n  const [userData, setUserData] = useState(sampleUserData);\n  const [posts, setPosts] = useState(samplePosts);\n  const [loading, setLoading] = useState(false);\n  \n  // Edit states\n  const [isEditingPersonalInfo, setIsEditingPersonalInfo] = useState(false);\n  const [isEditingProfilePhoto, setIsEditingProfilePhoto] = useState(false);\n  const [isEditingCoverPhoto, setIsEditingCoverPhoto] = useState(false);\n  const [isEditingBio, setIsEditingBio] = useState(false);\n  const [isEditingPrivacySettings, setIsEditingPrivacySettings] = useState(false);\n  \n  // Form data states\n  const [personalInfoForm, setPersonalInfoForm] = useState({\n    username: userData.username,\n    company: userData.company,\n    location: userData.location,\n    website: userData.website\n  });\n  \n  const [bioForm, setBioForm] = useState(userData.bio);\n  \n  // File upload states\n  const [profilePhotoFile, setProfilePhotoFile] = useState(null);\n  const [profilePhotoPreview, setProfilePhotoPreview] = useState(userData.profilePic);\n  \n  const [coverPhotoFile, setCoverPhotoFile] = useState(null);\n  const [coverPhotoPreview, setCoverPhotoPreview] = useState(userData.coverPic);\n  \n  // Active tab state\n  const [activeTab, setActiveTab] = useState('Posts');\n  \n  // Post filter state\n  const [postFilter, setPostFilter] = useState('All');\n  const [postView, setPostView] = useState('grid');\n  \n  // Follow state\n  const [isFollowing, setIsFollowing] = useState(false);\n  \n  // Handle personal info form changes\n  const handlePersonalInfoChange = (e) => {\n    const { name, value } = e.target;\n    setPersonalInfoForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  \n  // Handle bio form changes\n  const handleBioChange = (e) => {\n    setBioForm(e.target.value);\n  };\n  \n  // Handle profile photo changes\n  const handleProfilePhotoChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setProfilePhotoFile(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfilePhotoPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  \n  // Handle cover photo changes\n  const handleCoverPhotoChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setCoverPhotoFile(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setCoverPhotoPreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  \n  // Save personal info changes\n  const savePersonalInfo = () => {\n    setLoading(true);\n    // Simulate API call\n    setTimeout(() => {\n      setUserData(prev => ({\n        ...prev,\n        username: personalInfoForm.username,\n        company: personalInfoForm.company,\n        location: personalInfoForm.location,\n        website: personalInfoForm.website\n      }));\n      setIsEditingPersonalInfo(false);\n      setLoading(false);\n    }, 1000);\n  };\n  \n  // Save bio changes\n  const saveBio = () => {\n    setLoading(true);\n    // Simulate API call\n    setTimeout(() => {\n      setUserData(prev => ({\n        ...prev,\n        bio: bioForm\n      }));\n      setIsEditingBio(false);\n      setLoading(false);\n    }, 1000);\n  };\n  \n  // Save profile photo changes\n  const saveProfilePhoto = () => {\n    setLoading(true);\n    // Simulate API call\n    setTimeout(() => {\n      setUserData(prev => ({\n        ...prev,\n        profilePic: profilePhotoPreview\n      }));\n      setIsEditingProfilePhoto(false);\n      setLoading(false);\n    }, 1000);\n  };\n  \n  // Save cover photo changes\n  const saveCoverPhoto = () => {\n    setLoading(true);\n    // Simulate API call\n    setTimeout(() => {\n      setUserData(prev => ({\n        ...prev,\n        coverPic: coverPhotoPreview\n      }));\n      setIsEditingCoverPhoto(false);\n      setLoading(false);\n    }, 1000);\n  };\n  \n  // Toggle follow status\n  const toggleFollow = () => {\n    setIsFollowing(prev => !prev);\n    // Update follower count accordingly\n    setUserData(prev => ({\n      ...prev,\n      stats: {\n        ...prev.stats,\n        followers: isFollowing ? prev.stats.followers - 1 : prev.stats.followers + 1\n      }\n    }));\n  };\n  \n  // Filter posts based on active tab\n  const filteredPosts = () => {\n    switch (activeTab) {\n      case 'Posts':\n        return posts;\n      case 'Loved':\n        return posts.filter(post => post.likes > 300);\n      case 'Challenges':\n        return posts.filter(post => post.category === 'Social Issues');\n      case 'Activity':\n        return posts.sort((a, b) => new Date(b.date) - new Date(a.date));\n      case 'Saved':\n        return posts.filter(post => post.id % 2 === 0); // Just a sample filter\n      default:\n        return posts;\n    }\n  };\n  \n  return (\n    <ProfileContext.Provider\n      value={{\n        userData,\n        posts: filteredPosts(),\n        loading,\n        isEditingPersonalInfo,\n        setIsEditingPersonalInfo,\n        isEditingProfilePhoto,\n        setIsEditingProfilePhoto,\n        isEditingCoverPhoto,\n        setIsEditingCoverPhoto,\n        isEditingBio,\n        setIsEditingBio,\n        isEditingPrivacySettings,\n        setIsEditingPrivacySettings,\n        personalInfoForm,\n        handlePersonalInfoChange,\n        savePersonalInfo,\n        bioForm,\n        handleBioChange,\n        saveBio,\n        profilePhotoPreview,\n        handleProfilePhotoChange,\n        saveProfilePhoto,\n        coverPhotoPreview,\n        handleCoverPhotoChange,\n        saveCoverPhoto,\n        activeTab,\n        setActiveTab,\n        postFilter,\n        setPostFilter,\n        postView,\n        setPostView,\n        isFollowing,\n        toggleFollow\n      }}\n    >\n      {children}\n    </ProfileContext.Provider>\n  );\n}\n\nexport function useProfile() {\n  return useContext(ProfileContext);\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAIA,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,IAAI;IACJ,UAAU;IACV,QAAQ;IACR,KAAK;IACL,YAAY;IACZ,UAAU;IACV,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;QACL,OAAO;QACP,WAAW;QACX,WAAW;QACX,YAAY;QACZ,OAAO;IACT;IACA,WAAW;QACT;YACE,IAAI;YACJ,UAAU;YACV,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;QACd;KACD;AACH;AAEA,oBAAoB;AACpB,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,KAAK;QACL,UAAU;QACV,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,KAAK;QACL,UAAU;QACV,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,KAAK;QACL,UAAU;QACV,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,KAAK;QACL,UAAU;QACV,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,KAAK;QACL,UAAU;QACV,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,KAAK;QACL,UAAU;QACV,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,SAAS;IACX;CACD;AAED,qBAAqB;AACrB,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;AAE5B,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAC1C,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,cAAc;IACd,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,mBAAmB;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,UAAU,SAAS,QAAQ;QAC3B,SAAS,SAAS,OAAO;QACzB,UAAU,SAAS,QAAQ;QAC3B,SAAS,SAAS,OAAO;IAC3B;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,GAAG;IAEnD,qBAAqB;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,UAAU;IAElF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ;IAE5E,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oBAAoB;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,eAAe;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oCAAoC;IACpC,MAAM,2BAA2B,CAAC;QAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC;QACvB,WAAW,EAAE,MAAM,CAAC,KAAK;IAC3B;IAEA,+BAA+B;IAC/B,MAAM,2BAA2B,CAAC;QAChC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,oBAAoB;YACpB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,uBAAuB,OAAO,MAAM;YACtC;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,yBAAyB,CAAC;QAC9B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,kBAAkB;YAClB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,qBAAqB,OAAO,MAAM;YACpC;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB;QACvB,WAAW;QACX,oBAAoB;QACpB,WAAW;YACT,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU,iBAAiB,QAAQ;oBACnC,SAAS,iBAAiB,OAAO;oBACjC,UAAU,iBAAiB,QAAQ;oBACnC,SAAS,iBAAiB,OAAO;gBACnC,CAAC;YACD,yBAAyB;YACzB,WAAW;QACb,GAAG;IACL;IAEA,mBAAmB;IACnB,MAAM,UAAU;QACd,WAAW;QACX,oBAAoB;QACpB,WAAW;YACT,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,KAAK;gBACP,CAAC;YACD,gBAAgB;YAChB,WAAW;QACb,GAAG;IACL;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB;QACvB,WAAW;QACX,oBAAoB;QACpB,WAAW;YACT,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,YAAY;gBACd,CAAC;YACD,yBAAyB;YACzB,WAAW;QACb,GAAG;IACL;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB,WAAW;QACX,oBAAoB;QACpB,WAAW;YACT,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;gBACZ,CAAC;YACD,uBAAuB;YACvB,WAAW;QACb,GAAG;IACL;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,eAAe,CAAA,OAAQ,CAAC;QACxB,oCAAoC;QACpC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO;oBACL,GAAG,KAAK,KAAK;oBACb,WAAW,cAAc,KAAK,KAAK,CAAC,SAAS,GAAG,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG;gBAC7E;YACF,CAAC;IACH;IAEA,mCAAmC;IACnC,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG;YAC3C,KAAK;gBACH,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YAChD,KAAK;gBACH,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI;YAChE,KAAK;gBACH,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,GAAG,MAAM,IAAI,uBAAuB;YACzE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,eAAe,QAAQ;QACtB,OAAO;YACL;YACA,OAAO;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAnNgB;KAAA;AAqNT,SAAS;;IACd,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpB;IAFgB", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/CoverPhoto.js"], "sourcesContent": ["'use client';\r\n\r\nimport { FiMoreHorizontal, FiSettings, FiCamera } from 'react-icons/fi';\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\nimport { useState } from 'react';\r\n\r\nexport default function CoverPhoto() {\r\n  const {\r\n    coverPhotoPreview,\r\n    setIsEditingCoverPhoto\r\n  } = useProfile();\r\n\r\n  const [showOptions, setShowOptions] = useState(false);\r\n\r\n  const options = [\r\n    { text: 'Change cover photo', onClick: () => setIsEditingCoverPhoto(true) },\r\n    { text: 'View cover photo', onClick: () => window.open(coverPhotoPreview, '_blank') },\r\n    { text: 'Report cover photo', onClick: () => alert('Report feature coming soon!') },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <div className=\"h-[240px] rounded-lg overflow-hidden relative group\">\r\n        <img\r\n          src={coverPhotoPreview}\r\n          alt=\"Cover photo\"\r\n          className=\"w-full h-full object-cover\"\r\n        />\r\n        <div\r\n          className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-300\"\r\n          onClick={() => setIsEditingCoverPhoto(true)}\r\n        >\r\n          <div className=\"bg-black/50 rounded-full p-3 cursor-pointer\">\r\n            <FiCamera className=\"text-white text-2xl\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute top-4 right-4 flex gap-2\">\r\n        <div className=\"relative\">\r\n          <button\r\n            className=\"bg-white/80 hover:bg-white rounded-full p-2 transition-colors duration-200 backdrop-blur shadow-sm\"\r\n            onClick={() => setShowOptions(!showOptions)}\r\n          >\r\n            <FiMoreHorizontal className=\"text-xl\" />\r\n          </button>\r\n\r\n          {showOptions && (\r\n            <div className=\"absolute right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 p-2 w-48 z-10\">\r\n              <ul className=\"space-y-1\">\r\n                {options.map((option, index) => (\r\n                  <li\r\n                    key={index}\r\n                    onClick={option.onClick}\r\n                    className=\"px-3 py-2 hover:bg-gray-100 rounded-md transition-colors cursor-pointer text-sm\"\r\n                  >\r\n                    {option.text}\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          )}\r\n        </div>\r\n        <button\r\n          className=\"bg-white/80 hover:bg-white rounded-full p-2 transition-colors duration-200 backdrop-blur shadow-sm\"\r\n          onClick={() => alert('Settings feature coming soon!')}\r\n        >\r\n          <FiSettings className=\"text-xl\" />\r\n        </button>\r\n      </div>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EACJ,iBAAiB,EACjB,sBAAsB,EACvB,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,UAAU;QACd;YAAE,MAAM;YAAsB,SAAS,IAAM,uBAAuB;QAAM;QAC1E;YAAE,MAAM;YAAoB,SAAS,IAAM,OAAO,IAAI,CAAC,mBAAmB;QAAU;QACpF;YAAE,MAAM;YAAsB,SAAS,IAAM,MAAM;QAA+B;KACnF;IAED,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,KAAI;wBACJ,WAAU;;;;;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,uBAAuB;kCAEtC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAI1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe,CAAC;0CAE/B,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;4BAG7B,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4CAEC,SAAS,OAAO,OAAO;4CACvB,WAAU;sDAET,OAAO,IAAI;2CAJP;;;;;;;;;;;;;;;;;;;;;kCAWjB,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,MAAM;kCAErB,cAAA,6LAAC,iJAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;AAKhC;GAjEwB;;QAIlB,oIAAA,CAAA,aAAU;;;KAJQ", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/avatar.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\"relative flex size-8 shrink-0 overflow-hidden rounded-full\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAEf;KAVS;AAYT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAEf;KAfS", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/ProfileAvatar.js"], "sourcesContent": ["'use client';\r\n\r\nimport { FiCamera } from 'react-icons/fi';\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\nimport { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';\r\nimport { Button } from '@/components/ui/button';\r\n\r\nexport default function ProfileAvatar() {\r\n  const {\r\n    userData,\r\n    profilePhotoPreview,\r\n    setIsEditingProfilePhoto\r\n  } = useProfile();\r\n\r\n  return (\r\n    <div className=\"absolute -bottom-12 left-8 flex items-end\">\r\n      <div className=\"relative group\">\r\n        <Avatar className=\"h-[140px] w-[140px] border-4 border-white bg-white shadow-md\">\r\n          <AvatarImage\r\n            src={profilePhotoPreview}\r\n            alt={`${userData.username}'s avatar`}\r\n            className=\"object-cover\"\r\n          />\r\n          <AvatarFallback className=\"text-2xl\">\r\n            {userData.username.split(' ').map(name => name[0]).join('')}\r\n          </AvatarFallback>\r\n        </Avatar>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"absolute inset-0 bg-black/30 rounded-full opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-300\"\r\n          onClick={() => setIsEditingProfilePhoto(true)}\r\n        >\r\n          <FiCamera className=\"text-white text-3xl\" />\r\n        </Button>\r\n      </div>\r\n      <div className=\"ml-4 mb-4 flex items-center\">\r\n        <span className=\"bg-gradient-to-r from-primary-500 to-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium\">\r\n          Senior Editor\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EACJ,QAAQ,EACR,mBAAmB,EACnB,wBAAwB,EACzB,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,6LAAC,qIAAA,CAAA,cAAW;gCACV,KAAK;gCACL,KAAK,GAAG,SAAS,QAAQ,CAAC,SAAS,CAAC;gCACpC,WAAU;;;;;;0CAEZ,6LAAC,qIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACvB,SAAS,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;kCAG5D,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,yBAAyB;kCAExC,cAAA,6LAAC,iJAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAAsG;;;;;;;;;;;;;;;;;AAM9H;GApCwB;;QAKlB,oIAAA,CAAA,aAAU;;;KALQ", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/dropdown-menu.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.Trigger data-slot=\"dropdown-menu-trigger\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props} />\r\n    </DropdownMenuPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}>\r\n      <span\r\n        className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.RadioGroup data-slot=\"dropdown-menu-radio-group\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <span\r\n        className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\"text-muted-foreground ml-auto text-xs tracking-widest\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACJ;IACC,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAClF;MAJS;AAMT,SAAS,oBAAoB,EAC3B,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,UAA6B;QAAC,aAAU;QAAyB,GAAG,KAAK;;;;;;AACpF;MAJS;AAMT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAGjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAChF;MAJS;AAMT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAEf;MAjBS;AAmBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BACT,6LAAC;gBACC,WAAU;0BACV,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,aAAgC;QAAC,aAAU;QAA6B,GAAG,KAAK;;;;;;AAC3F;MAJS;AAMT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BACT,6LAAC;gBACC,WAAU;0BACV,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAEf;MAZS;AAcT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAEf;OAVS;AAYT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEf;OAVS;AAYT,SAAS,gBAAgB,EACvB,GAAG,OACJ;IACC,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YACR;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAnBS;AAqBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAEf;OAbS", "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/ProfileHeader.js"], "sourcesContent": ["'use client';\r\n\r\nimport { FiEdit, FiMail, FiUserPlus, FiShare2, FiCamera, FiEye, FiUserCheck, FiCopy, FiTwitter, FiFacebook, FiLinkedin } from 'react-icons/fi';\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\n// No need for useState with shadcn dropdowns\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator\r\n} from '@/components/ui/dropdown-menu';\r\nimport { toast } from 'sonner';\r\n\r\nexport default function ProfileHeader() {\r\n  const {\r\n    setIsEditingPersonalInfo,\r\n    setIsEditingProfilePhoto,\r\n    setIsEditingCoverPhoto,\r\n    setIsEditingBio,\r\n    setIsEditingPrivacySettings,\r\n    isFollowing,\r\n    toggleFollow\r\n  } = useProfile();\r\n\r\n  // No need for state variables with shadcn dropdowns\r\n\r\n  const editOptions = [\r\n    {\r\n      icon: <FiUserPlus />,\r\n      text: 'Edit personal info',\r\n      onClick: () => {\r\n        setIsEditingPersonalInfo(true);\r\n        setIsDetailsOpen(false);\r\n      }\r\n    },\r\n    {\r\n      icon: <FiCamera />,\r\n      text: 'Change profile photo',\r\n      onClick: () => {\r\n        setIsEditingProfilePhoto(true);\r\n        setIsDetailsOpen(false);\r\n      }\r\n    },\r\n    {\r\n      icon: <FiCamera />,\r\n      text: 'Change cover photo',\r\n      onClick: () => {\r\n        setIsEditingCoverPhoto(true);\r\n        setIsDetailsOpen(false);\r\n      }\r\n    },\r\n    {\r\n      icon: <FiEdit />,\r\n      text: 'Update bio',\r\n      onClick: () => {\r\n        setIsEditingBio(true);\r\n        setIsDetailsOpen(false);\r\n      }\r\n    },\r\n    {\r\n      icon: <FiEye />,\r\n      text: 'Privacy settings',\r\n      onClick: () => {\r\n        setIsEditingPrivacySettings(true);\r\n        setIsDetailsOpen(false);\r\n      }\r\n    },\r\n  ];\r\n\r\n  // Share options are now directly in the dropdown menu\r\n\r\n  const copyProfileLink = () => {\r\n    navigator.clipboard.writeText(window.location.href);\r\n    toast.success('Profile link copied to clipboard!');\r\n  };\r\n\r\n  const shareOnTwitter = () => {\r\n    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out this profile!`, '_blank');\r\n    toast.success('Shared on Twitter');\r\n  };\r\n\r\n  const shareOnFacebook = () => {\r\n    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`, '_blank');\r\n    toast.success('Shared on Facebook');\r\n  };\r\n\r\n  const shareOnLinkedIn = () => {\r\n    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`, '_blank');\r\n    toast.success('Shared on LinkedIn');\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-end gap-3 mt-6\">\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"outline\" className=\"rounded-full flex items-center gap-2\">\r\n            <FiEdit className=\"h-4 w-4\" />\r\n            Edit Profile\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\" className=\"w-56\">\r\n          {editOptions.map((option, index) => (\r\n            <DropdownMenuItem\r\n              key={index}\r\n              onClick={option.onClick}\r\n              className=\"cursor-pointer flex items-center gap-2\"\r\n            >\r\n              {option.icon}\r\n              <span>{option.text}</span>\r\n            </DropdownMenuItem>\r\n          ))}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n\r\n      <Button\r\n        variant=\"default\"\r\n        className=\"rounded-full\"\r\n        onClick={() => toast.info('Message feature coming soon!')}\r\n      >\r\n        <FiMail className=\"mr-2 h-4 w-4\" />\r\n        Message\r\n      </Button>\r\n\r\n      <Button\r\n        variant={isFollowing ? \"outline\" : \"secondary\"}\r\n        className=\"rounded-full\"\r\n        onClick={toggleFollow}\r\n      >\r\n        {isFollowing ? <FiUserCheck className=\"mr-2 h-4 w-4\" /> : <FiUserPlus className=\"mr-2 h-4 w-4\" />}\r\n        {isFollowing ? 'Following' : 'Follow'}\r\n      </Button>\r\n\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"outline\" size=\"icon\" className=\"rounded-full\">\r\n            <FiShare2 className=\"h-4 w-4\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n          <DropdownMenuItem onClick={copyProfileLink} className=\"cursor-pointer\">\r\n            <FiCopy className=\"mr-2 h-4 w-4\" />\r\n            <span>Copy profile link</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem onClick={shareOnTwitter} className=\"cursor-pointer\">\r\n            <FiTwitter className=\"mr-2 h-4 w-4\" />\r\n            <span>Share on Twitter</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={shareOnFacebook} className=\"cursor-pointer\">\r\n            <FiFacebook className=\"mr-2 h-4 w-4\" />\r\n            <span>Share on Facebook</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={shareOnLinkedIn} className=\"cursor-pointer\">\r\n            <FiLinkedin className=\"mr-2 h-4 w-4\" />\r\n            <span>Share on LinkedIn</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,6CAA6C;AAC7C;AACA;AAOA;;;AAbA;;;;;;AAee,SAAS;;IACtB,MAAM,EACJ,wBAAwB,EACxB,wBAAwB,EACxB,sBAAsB,EACtB,eAAe,EACf,2BAA2B,EAC3B,WAAW,EACX,YAAY,EACb,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,oDAAoD;IAEpD,MAAM,cAAc;QAClB;YACE,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;YACjB,MAAM;YACN,SAAS;gBACP,yBAAyB;gBACzB,iBAAiB;YACnB;QACF;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;;;;;YACf,MAAM;YACN,SAAS;gBACP,yBAAyB;gBACzB,iBAAiB;YACnB;QACF;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;;;;;YACf,MAAM;YACN,SAAS;gBACP,uBAAuB;gBACvB,iBAAiB;YACnB;QACF;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;YACb,MAAM;YACN,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,QAAK;;;;;YACZ,MAAM;YACN,SAAS;gBACP,4BAA4B;gBAC5B,iBAAiB;YACnB;QACF;KACD;IAED,sDAAsD;IAEtD,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;QAClD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC,CAAC,qCAAqC,EAAE,mBAAmB,OAAO,QAAQ,CAAC,IAAI,EAAE,6BAA6B,CAAC,EAAE;QAC7H,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,CAAC,6CAA6C,EAAE,mBAAmB,OAAO,QAAQ,CAAC,IAAI,GAAG,EAAE;QACxG,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,CAAC,oDAAoD,EAAE,mBAAmB,OAAO,QAAQ,CAAC,IAAI,GAAG,EAAE;QAC/G,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;;8CAClC,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAM,WAAU;kCACxC,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC,+IAAA,CAAA,mBAAgB;gCAEf,SAAS,OAAO,OAAO;gCACvB,WAAU;;oCAET,OAAO,IAAI;kDACZ,6LAAC;kDAAM,OAAO,IAAI;;;;;;;+BALb;;;;;;;;;;;;;;;;0BAWb,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,WAAU;gBACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;;kCAE1B,6LAAC,iJAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIrC,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,cAAc,YAAY;gBACnC,WAAU;gBACV,SAAS;;oBAER,4BAAc,6LAAC,iJAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAAoB,6LAAC,iJAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;oBAC/E,cAAc,cAAc;;;;;;;0BAG/B,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,WAAU;sCAC9C,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGxB,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAM,WAAU;;0CACzC,6LAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAiB,WAAU;;kDACpD,6LAAC,iJAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAgB,WAAU;;kDACnD,6LAAC,iJAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAiB,WAAU;;kDACpD,6LAAC,iJAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAiB,WAAU;;kDACpD,6LAAC,iJAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAnJwB;;QASlB,oIAAA,CAAA,aAAU;;;KATQ", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/ProfileInfo.js"], "sourcesContent": ["'use client';\r\n\r\nimport { FiBriefcase, FiMapPin, FiLink, FiCalendar, FiEdit } from 'react-icons/fi';\r\nimport { FaCheckCircle } from 'react-icons/fa';\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\n\r\nexport default function ProfileInfo() {\r\n  const {\r\n    userData,\r\n    setIsEditingBio,\r\n    setIsEditingPersonalInfo\r\n  } = useProfile();\r\n\r\n  const getWebsiteDisplay = () => {\r\n    if (!userData.website) return '';\r\n\r\n    // Remove http:// or https:// for display purposes\r\n    let displayUrl = userData.website;\r\n    if (displayUrl.startsWith('http://')) {\r\n      displayUrl = displayUrl.substring(7);\r\n    } else if (displayUrl.startsWith('https://')) {\r\n      displayUrl = displayUrl.substring(8);\r\n    }\r\n\r\n    return displayUrl;\r\n  };\r\n\r\n  const infoItems = [\r\n    {\r\n      icon: <FiBriefcase className=\"text-gray-500 text-lg\" />,\r\n      text: userData.company || 'Add workplace',\r\n      onClick: () => setIsEditingPersonalInfo(true)\r\n    },\r\n    {\r\n      icon: <FiMapPin className=\"text-gray-500 text-lg\" />,\r\n      text: userData.location || 'Add location',\r\n      onClick: () => setIsEditingPersonalInfo(true)\r\n    },\r\n    {\r\n      icon: <FiLink className=\"text-gray-500 text-lg\" />,\r\n      text: userData.website ? (\r\n        <a href={userData.website} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-primary-600 hover:underline\">\r\n          {getWebsiteDisplay()}\r\n        </a>\r\n      ) : (\r\n        <span className=\"text-gray-400\">Add website</span>\r\n      ),\r\n      onClick: () => setIsEditingPersonalInfo(true)\r\n    },\r\n    {\r\n      icon: <FiCalendar className=\"text-gray-500 text-lg\" />,\r\n      text: `Joined ${userData.joinDate}`\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"max-w-2xl mb-8\">\r\n      <div className=\"flex items-center mb-1\">\r\n        <h1 className=\"text-3xl font-bold mr-2\">{userData.username}</h1>\r\n        {userData.verified && <FaCheckCircle className=\"text-primary-500\" />}\r\n      </div>\r\n      <h2 className=\"text-lg text-gray-600 mb-2\">{userData.handle}</h2>\r\n      <div className=\"group relative\">\r\n        <p className=\"text-gray-700 mb-4 pr-6\">\r\n          {userData.bio}\r\n        </p>\r\n        <button\r\n          className=\"absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n          onClick={() => setIsEditingBio(true)}\r\n        >\r\n          <FiEdit className=\"text-gray-400 hover:text-gray-600\" />\r\n        </button>\r\n      </div>\r\n      <div className=\"flex flex-wrap gap-3 text-sm\">\r\n        {infoItems.map((item, index) => (\r\n          <span\r\n            key={index}\r\n            className={`flex items-center gap-1 ${item.onClick ? 'cursor-pointer hover:text-primary-600' : ''}`}\r\n            onClick={item.onClick}\r\n          >\r\n            {item.icon}\r\n            {item.text}\r\n          </span>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,wBAAwB,EACzB,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,OAAO,EAAE,OAAO;QAE9B,kDAAkD;QAClD,IAAI,aAAa,SAAS,OAAO;QACjC,IAAI,WAAW,UAAU,CAAC,YAAY;YACpC,aAAa,WAAW,SAAS,CAAC;QACpC,OAAO,IAAI,WAAW,UAAU,CAAC,aAAa;YAC5C,aAAa,WAAW,SAAS,CAAC;QACpC;QAEA,OAAO;IACT;IAEA,MAAM,YAAY;QAChB;YACE,oBAAM,6LAAC,iJAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,MAAM,SAAS,OAAO,IAAI;YAC1B,SAAS,IAAM,yBAAyB;QAC1C;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM,SAAS,QAAQ,IAAI;YAC3B,SAAS,IAAM,yBAAyB;QAC1C;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,MAAM,SAAS,OAAO,iBACpB,6LAAC;gBAAE,MAAM,SAAS,OAAO;gBAAE,QAAO;gBAAS,KAAI;gBAAsB,WAAU;0BAC5E;;;;;qCAGH,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;YAElC,SAAS,IAAM,yBAAyB;QAC1C;QACA;YACE,oBAAM,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,MAAM,CAAC,OAAO,EAAE,SAAS,QAAQ,EAAE;QACrC;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2B,SAAS,QAAQ;;;;;;oBACzD,SAAS,QAAQ,kBAAI,6LAAC,iJAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;;0BAEjD,6LAAC;gBAAG,WAAU;0BAA8B,SAAS,MAAM;;;;;;0BAC3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCACV,SAAS,GAAG;;;;;;kCAEf,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,gBAAgB;kCAE/B,cAAA,6LAAC,iJAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGtB,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;wBAEC,WAAW,CAAC,wBAAwB,EAAE,KAAK,OAAO,GAAG,0CAA0C,IAAI;wBACnG,SAAS,KAAK,OAAO;;4BAEpB,KAAK,IAAI;4BACT,KAAK,IAAI;;uBALL;;;;;;;;;;;;;;;;AAWjB;GAjFwB;;QAKlB,oIAAA,CAAA,aAAU;;;KALQ", "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/ProfileStats.js"], "sourcesContent": ["'use client';\r\n\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\nimport { useState } from 'react';\r\n\r\nexport default function ProfileStats() {\r\n  const { userData } = useProfile();\r\n  const [showFollowersModal, setShowFollowersModal] = useState(false);\r\n  const [showFollowingModal, setShowFollowingModal] = useState(false);\r\n\r\n  // Format numbers for display (e.g., 1500 -> 1.5K)\r\n  const formatNumber = (num) => {\r\n    if (num >= 1000000) {\r\n      return (num / 1000000).toFixed(1).replace(/\\.0$/, '') + 'M';\r\n    }\r\n    if (num >= 1000) {\r\n      return (num / 1000).toFixed(1).replace(/\\.0$/, '') + 'K';\r\n    }\r\n    return num.toString();\r\n  };\r\n\r\n  const stats = [\r\n    {\r\n      value: formatNumber(userData.stats.posts),\r\n      label: 'Posts',\r\n      onClick: () => alert('View all posts')\r\n    },\r\n    {\r\n      value: formatNumber(userData.stats.followers),\r\n      label: 'Followers',\r\n      onClick: () => setShowFollowersModal(true)\r\n    },\r\n    {\r\n      value: formatNumber(userData.stats.following),\r\n      label: 'Following',\r\n      onClick: () => setShowFollowingModal(true)\r\n    },\r\n    {\r\n      value: formatNumber(userData.stats.challenges),\r\n      label: 'Challenges',\r\n      onClick: () => alert('View all challenges')\r\n    },\r\n    {\r\n      value: formatNumber(userData.stats.loves),\r\n      label: 'Loves',\r\n      onClick: () => alert('View all loved posts')\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex overflow-x-auto mb-10 pb-2 gap-8 justify-between\">\r\n        <div className=\"flex gap-8\">\r\n          {stats.map((stat, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"text-center cursor-pointer group\"\r\n              onClick={stat.onClick}\r\n            >\r\n              <div className=\"text-2xl font-bold group-hover:text-primary-600 transition-colors\">\r\n                {stat.value}\r\n              </div>\r\n              <div className=\"text-gray-600 text-sm\">{stat.label}</div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"flex -space-x-2\">\r\n            {userData.followers.slice(0, 3).map((follower, index) => (\r\n              <img\r\n                key={index}\r\n                src={follower.profilePic}\r\n                alt={`${follower.username}'s profile`}\r\n                className=\"w-8 h-8 rounded-full border-2 border-white\"\r\n              />\r\n            ))}\r\n          </div>\r\n          <div className=\"text-sm text-gray-600\">\r\n            {userData.followers.length > 0 ? (\r\n              <>\r\n                <span className=\"font-bold text-gray-800\">{userData.followers[0].username}</span>\r\n                {userData.followers.length > 1 && (\r\n                  <>\r\n                    {' '}and{' '}\r\n                    <span\r\n                      className=\"font-bold text-gray-800 cursor-pointer hover:text-primary-600\"\r\n                      onClick={() => setShowFollowersModal(true)}\r\n                    >\r\n                      {userData.followers.length - 1} {userData.followers.length === 2 ? 'other' : 'others'}\r\n                    </span>\r\n                  </>\r\n                )}\r\n                {' '}follow you\r\n              </>\r\n            ) : (\r\n              <span>No followers yet</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Followers Modal */}\r\n      {showFollowersModal && (\r\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\" onClick={() => setShowFollowersModal(false)}>\r\n          <div className=\"bg-white rounded-lg w-full max-w-md p-6 relative\" onClick={e => e.stopPropagation()}>\r\n            <h2 className=\"text-xl font-bold mb-4\">Followers</h2>\r\n            <div className=\"max-h-80 overflow-y-auto\">\r\n              {userData.followers.length > 0 ? (\r\n                <ul className=\"space-y-3\">\r\n                  {userData.followers.map((follower, index) => (\r\n                    <li key={index} className=\"flex items-center gap-3 p-2 hover:bg-gray-50 rounded-md\">\r\n                      <img\r\n                        src={follower.profilePic}\r\n                        alt={`${follower.username}'s profile`}\r\n                        className=\"w-10 h-10 rounded-full\"\r\n                      />\r\n                      <div>\r\n                        <p className=\"font-medium\">{follower.username}</p>\r\n                      </div>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              ) : (\r\n                <p className=\"text-gray-500 text-center py-8\">No followers yet</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Following Modal */}\r\n      {showFollowingModal && (\r\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\" onClick={() => setShowFollowingModal(false)}>\r\n          <div className=\"bg-white rounded-lg w-full max-w-md p-6 relative\" onClick={e => e.stopPropagation()}>\r\n            <h2 className=\"text-xl font-bold mb-4\">Following</h2>\r\n            <div className=\"max-h-80 overflow-y-auto\">\r\n              <p className=\"text-gray-500 text-center py-8\">Following list coming soon</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,kDAAkD;IAClD,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QAC1D;QACA,IAAI,OAAO,MAAM;YACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACvD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,MAAM,QAAQ;QACZ;YACE,OAAO,aAAa,SAAS,KAAK,CAAC,KAAK;YACxC,OAAO;YACP,SAAS,IAAM,MAAM;QACvB;QACA;YACE,OAAO,aAAa,SAAS,KAAK,CAAC,SAAS;YAC5C,OAAO;YACP,SAAS,IAAM,sBAAsB;QACvC;QACA;YACE,OAAO,aAAa,SAAS,KAAK,CAAC,SAAS;YAC5C,OAAO;YACP,SAAS,IAAM,sBAAsB;QACvC;QACA;YACE,OAAO,aAAa,SAAS,KAAK,CAAC,UAAU;YAC7C,OAAO;YACP,SAAS,IAAM,MAAM;QACvB;QACA;YACE,OAAO,aAAa,SAAS,KAAK,CAAC,KAAK;YACxC,OAAO;YACP,SAAS,IAAM,MAAM;QACvB;KACD;IAED,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,WAAU;gCACV,SAAS,KAAK,OAAO;;kDAErB,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAU;kDAAyB,KAAK,KAAK;;;;;;;+BAP7C;;;;;;;;;;kCAWX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,SAAS,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBAC7C,6LAAC;wCAEC,KAAK,SAAS,UAAU;wCACxB,KAAK,GAAG,SAAS,QAAQ,CAAC,UAAU,CAAC;wCACrC,WAAU;uCAHL;;;;;;;;;;0CAOX,6LAAC;gCAAI,WAAU;0CACZ,SAAS,SAAS,CAAC,MAAM,GAAG,kBAC3B;;sDACE,6LAAC;4CAAK,WAAU;sDAA2B,SAAS,SAAS,CAAC,EAAE,CAAC,QAAQ;;;;;;wCACxE,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B;;gDACG;gDAAI;gDAAI;8DACT,6LAAC;oDACC,WAAU;oDACV,SAAS,IAAM,sBAAsB;;wDAEpC,SAAS,SAAS,CAAC,MAAM,GAAG;wDAAE;wDAAE,SAAS,SAAS,CAAC,MAAM,KAAK,IAAI,UAAU;;;;;;;;;wCAIlF;wCAAI;;iEAGP,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAOb,oCACC,6LAAC;gBAAI,WAAU;gBAAkE,SAAS,IAAM,sBAAsB;0BACpH,cAAA,6LAAC;oBAAI,WAAU;oBAAmD,SAAS,CAAA,IAAK,EAAE,eAAe;;sCAC/F,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BAAI,WAAU;sCACZ,SAAS,SAAS,CAAC,MAAM,GAAG,kBAC3B,6LAAC;gCAAG,WAAU;0CACX,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDACC,KAAK,SAAS,UAAU;gDACxB,KAAK,GAAG,SAAS,QAAQ,CAAC,UAAU,CAAC;gDACrC,WAAU;;;;;;0DAEZ,6LAAC;0DACC,cAAA,6LAAC;oDAAE,WAAU;8DAAe,SAAS,QAAQ;;;;;;;;;;;;uCAPxC;;;;;;;;;qDAab,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;;;;;YAQvD,oCACC,6LAAC;gBAAI,WAAU;gBAAkE,SAAS,IAAM,sBAAsB;0BACpH,cAAA,6LAAC;oBAAI,WAAU;oBAAmD,SAAS,CAAA,IAAK,EAAE,eAAe;;sCAC/F,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;GA1IwB;;QACD,oIAAA,CAAA,aAAU;;;KADT", "debugId": null}}, {"offset": {"line": 1929, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/tabs.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAEf;KAVS;AAYT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAEf;MAVS", "debugId": null}}, {"offset": {"line": 2007, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/ProfileNav.js"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON>FileText, FiHeart, FiAward, FiClock, FiBookmark } from 'react-icons/fi';\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\nimport { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\n\r\nexport default function ProfileNav() {\r\n  const { activeTab, setActiveTab } = useProfile();\r\n\r\n  const navItems = [\r\n    { icon: <FiFileText />, label: 'Posts' },\r\n    { icon: <FiHeart />, label: 'Loved' },\r\n    { icon: <FiAward />, label: 'Challenges' },\r\n    { icon: <FiClock />, label: 'Activity' },\r\n    { icon: <FiBookmark />, label: 'Saved' },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"border-b border-gray-200\">\r\n      <Tabs\r\n        value={activeTab}\r\n        onValueChange={setActiveTab}\r\n        className=\"w-full\"\r\n      >\r\n        <TabsList className=\"bg-transparent h-auto p-0 w-full justify-start overflow-x-auto\">\r\n          {navItems.map((item, index) => (\r\n            <TabsTrigger\r\n              key={index}\r\n              value={item.label}\r\n              className=\"px-4 py-2 data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:text-primary-600 data-[state=active]:bg-transparent data-[state=active]:shadow-none rounded-none flex items-center gap-2\"\r\n            >\r\n              {item.icon}\r\n              {item.label}\r\n            </TabsTrigger>\r\n          ))}\r\n        </TabsList>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAE7C,MAAM,WAAW;QACf;YAAE,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;YAAK,OAAO;QAAQ;QACvC;YAAE,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;YAAK,OAAO;QAAQ;QACpC;YAAE,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;YAAK,OAAO;QAAa;QACzC;YAAE,oBAAM,6LAAC,iJAAA,CAAA,UAAO;;;;;YAAK,OAAO;QAAW;QACvC;YAAE,oBAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;YAAK,OAAO;QAAQ;KACxC;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YACH,OAAO;YACP,eAAe;YACf,WAAU;sBAEV,cAAA,6LAAC,mIAAA,CAAA,WAAQ;gBAAC,WAAU;0BACjB,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,mIAAA,CAAA,cAAW;wBAEV,OAAO,KAAK,KAAK;wBACjB,WAAU;;4BAET,KAAK,IAAI;4BACT,KAAK,KAAK;;uBALN;;;;;;;;;;;;;;;;;;;;AAYnB;GAjCwB;;QACc,oIAAA,CAAA,aAAU;;;KADxB", "debugId": null}}, {"offset": {"line": 2118, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/PostCard.js"], "sourcesContent": ["import { FiHeart, FiMessageCircle, FiBookmark, FiPlayCircle } from 'react-icons/fi';\r\n\r\nexport default function PostCard({ post }) {\r\n  return (\r\n    <article className=\"bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 group\">\r\n      <div className=\"h-48 overflow-hidden\">\r\n        {post.isVideo ? (\r\n          <div className=\"w-full h-full bg-gray-100 flex items-center justify-center\">\r\n            <FiPlayCircle className=\"text-6xl text-gray-400\" />\r\n          </div>\r\n        ) : (\r\n          <img\r\n            src={post.image}\r\n            alt={post.alt}\r\n            className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-500\"\r\n          />\r\n        )}\r\n      </div>\r\n      <div className=\"p-5\">\r\n        <div className=\"flex justify-between items-center mb-3\">\r\n          <span\r\n            className={`text-xs font-medium py-1 px-2 rounded-full ${\r\n              post.category === 'Technology'\r\n                ? 'text-primary-600 bg-primary-50'\r\n                : post.category === 'Politics'\r\n                ? 'text-purple-600 bg-purple-50'\r\n                : post.category === 'Social Issues'\r\n                ? 'text-green-600 bg-green-50'\r\n                : post.category === 'Video'\r\n                ? 'text-red-600 bg-red-50'\r\n                : 'text-blue-600 bg-blue-50'\r\n            }`}\r\n          >\r\n            {post.category}\r\n          </span>\r\n          <span className=\"text-xs text-gray-500\">{post.date}</span>\r\n        </div>\r\n        <h3 className=\"font-bold text-lg mb-2 group-hover:text-primary-600 transition-colors duration-200\">\r\n          {post.title}\r\n        </h3>\r\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">{post.description}</p>\r\n        <div className=\"flex justify-between items-center\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <button className=\"hover:text-primary-600 transition-colors duration-200 flex items-center gap-1\">\r\n              <FiHeart />\r\n              <span className=\"text-sm\">{post.likes}</span>\r\n            </button>\r\n            <button className=\"hover:text-primary-600 transition-colors duration-200 flex items-center gap-1\">\r\n              <FiMessageCircle />\r\n              <span className=\"text-sm\">{post.comments}</span>\r\n            </button>\r\n          </div>\r\n          <button className=\"hover:text-primary-600 transition-colors duration-200\">\r\n            <FiBookmark />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </article>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,SAAS,EAAE,IAAI,EAAE;IACvC,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;0BACZ,KAAK,OAAO,iBACX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iJAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;yCAG1B,6LAAC;oBACC,KAAK,KAAK,KAAK;oBACf,KAAK,KAAK,GAAG;oBACb,WAAU;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,CAAC,2CAA2C,EACrD,KAAK,QAAQ,KAAK,eACd,mCACA,KAAK,QAAQ,KAAK,aAClB,iCACA,KAAK,QAAQ,KAAK,kBAClB,+BACA,KAAK,QAAQ,KAAK,UAClB,2BACA,4BACJ;0CAED,KAAK,QAAQ;;;;;;0CAEhB,6LAAC;gCAAK,WAAU;0CAAyB,KAAK,IAAI;;;;;;;;;;;;kCAEpD,6LAAC;wBAAG,WAAU;kCACX,KAAK,KAAK;;;;;;kCAEb,6LAAC;wBAAE,WAAU;kCAA2C,KAAK,WAAW;;;;;;kCACxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,iJAAA,CAAA,UAAO;;;;;0DACR,6LAAC;gDAAK,WAAU;0DAAW,KAAK,KAAK;;;;;;;;;;;;kDAEvC,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,iJAAA,CAAA,kBAAe;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAW,KAAK,QAAQ;;;;;;;;;;;;;;;;;;0CAG5C,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,iJAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvB;KAzDwB", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/PostsSection.js"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rid, FiList } from 'react-icons/fi';\r\nimport { useProfile } from '@/contexts/ProfileContext';\r\nimport { useState } from 'react';\r\nimport PostCard from './PostCard';\r\n\r\nexport default function PostsSection() {\r\n  const posts = [\r\n    {\r\n      image: 'https://images.unsplash.com/photo-1581905764498-f1b60bae941a?auto=format&fit=crop&q=80&w=400',\r\n      alt: 'Tech article thumbnail',\r\n      category: 'Technology',\r\n      date: 'June 15, 2023',\r\n      title: 'The Future of AI in Newsrooms: Revolutionizing Journalism',\r\n      description:\r\n        'Exploring how artificial intelligence is transforming the way journalists research, write, and distribute news content in the digital age.',\r\n      likes: '345',\r\n      comments: '42',\r\n      isVideo: false,\r\n    },\r\n    {\r\n      image: 'https://images.unsplash.com/photo-1494059980473-813e73ee784b?auto=format&fit=crop&q=80&w=400',\r\n      alt: 'Politics article thumbnail',\r\n      category: 'Politics',\r\n      date: 'May 28, 2023',\r\n      title: 'Analysis: How Recent Policy Changes Impact Climate Initiatives',\r\n      description:\r\n        'A deep dive into the latest legislative developments and their potential long-term effects on global climate action and sustainability efforts.',\r\n      likes: '287',\r\n      comments: '63',\r\n      isVideo: false,\r\n    },\r\n    {\r\n      image: 'https://images.unsplash.com/photo-1532938911079-1b06ac7ceec7?auto=format&fit=crop&q=80&w=400',\r\n      alt: 'Social issues article thumbnail',\r\n      category: 'Social Issues',\r\n      date: 'April 10, 2023',\r\n      title: 'The Housing Crisis: Innovative Solutions Emerging Nationwide',\r\n      description:\r\n        'Examining creative approaches to affordable housing that cities across the country are implementing to address growing concerns.',\r\n      likes: '512',\r\n      comments: '87',\r\n      isVideo: false,\r\n    },\r\n    {\r\n      image: '',\r\n      alt: '',\r\n      category: 'Video',\r\n      date: 'March 22, 2023',\r\n      title: 'Interview: Tech CEO on Industry’s Responsibility in the AI Era',\r\n      description:\r\n        'An exclusive conversation with a leading tech executive about ethical considerations and corporate accountability in artificial intelligence development.',\r\n      likes: '267',\r\n      comments: '32',\r\n      isVideo: true,\r\n    },\r\n    {\r\n      image: 'https://images.unsplash.com/photo-*************-54f48f0af0ee?auto=format&fit=crop&q=80&w=400',\r\n      alt: 'Travel article thumbnail',\r\n      category: 'Travel',\r\n      date: 'February 8, 2023',\r\n      title: 'Post-Pandemic Tourism: How Cities Are Reinventing Themselves',\r\n      description:\r\n        'From cultural initiatives to infrastructure improvements, discover how urban destinations are attracting visitors in a changed travel landscape.',\r\n      likes: '423',\r\n      comments: '54',\r\n      isVideo: false,\r\n    },\r\n  ];\r\n\r\n  const filterOptions = ['Most Recent', 'Most Popular', 'Articles Only', 'Videos Only'];\r\n\r\n  return (\r\n    <div className=\"mt-6\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h3 className=\"text-lg font-semibold\">Latest Posts</h3>\r\n        <div className=\"flex items-center gap-2\">\r\n          <details className=\"relative\">\r\n            <summary className=\"list-none cursor-pointer\">\r\n              <button className=\"bg-gray-100 hover:bg-gray-200 rounded-full py-1 px-3 text-sm font-medium flex items-center gap-1 transition-colors duration-200\">\r\n                <FiFilter className=\"text-sm\" />\r\n                Filter\r\n              </button>\r\n            </summary>\r\n            <div className=\"absolute right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 p-2 w-48 z-10\">\r\n              <ul className=\"space-y-1\">\r\n                {filterOptions.map((option, index) => (\r\n                  <li\r\n                    key={index}\r\n                    className=\"px-3 py-2 hover:bg-gray-100 rounded-md transition-colors cursor-pointer\"\r\n                  >\r\n                    {option}\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          </details>\r\n          <div className=\"flex rounded-full border border-gray-200 overflow-hidden\">\r\n            <button className=\"bg-white hover:bg-gray-100 py-1 px-3 flex items-center justify-center transition-colors duration-200 border-r border-gray-200\">\r\n              <FiGrid />\r\n            </button>\r\n            <button className=\"bg-white hover:bg-gray-100 py-1 px-3 flex items-center justify-center transition-colors duration-200\">\r\n              <FiList />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        {posts.map((post, index) => (\r\n          <PostCard key={index} post={post} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,MAAM;YACN,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,MAAM;YACN,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,MAAM;YACN,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,MAAM;YACN,OAAO;YAC<PERSON>,aACE;YAC<PERSON>,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,MAAM;YACN,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;YACV,SAAS;QACX;KACD;IAED,MAAM,gBAAgB;QAAC;QAAe;QAAgB;QAAiB;KAAc;IAErF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAQ,WAAU;;kDACjB,6LAAC;wCAAQ,WAAU;kDACjB,cAAA,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC,iJAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIpC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDACX,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;;;;;;0CASf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;kDAET,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKf,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,2IAAA,CAAA,UAAQ;wBAAa,MAAM;uBAAb;;;;;;;;;;;;;;;;AAKzB;KA5GwB", "debugId": null}}, {"offset": {"line": 2529, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/input.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({\r\n  className,\r\n  type,\r\n  ...props\r\n}) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,IAAI,EACJ,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAEf;KAjBS", "debugId": null}}, {"offset": {"line": 2563, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/label.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAEf;KAbS", "debugId": null}}, {"offset": {"line": 2599, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}>\r\n        {children}\r\n        <DialogPrimitive.Close\r\n          className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBACR;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,WAAU;;0CACV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEf;MAVS", "debugId": null}}, {"offset": {"line": 2798, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/EditProfileModal.js"], "sourcesContent": ["'use client';\n\nimport { useProfile } from '@/contexts/ProfileContext';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogFooter\n} from '@/components/ui/dialog';\n\nexport default function EditProfileModal() {\n  const {\n    isEditingPersonalInfo,\n    setIsEditingPersonalInfo,\n    personalInfoForm,\n    handlePersonalInfoChange,\n    savePersonalInfo,\n    loading\n  } = useProfile();\n\n  return (\n    <Dialog open={isEditingPersonalInfo} onOpenChange={setIsEditingPersonalInfo}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>Edit Personal Information</DialogTitle>\n        </DialogHeader>\n\n        <form onSubmit={(e) => {\n          e.preventDefault();\n          savePersonalInfo();\n        }}>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"username\">Name</Label>\n              <Input\n                id=\"username\"\n                name=\"username\"\n                value={personalInfoForm.username}\n                onChange={handlePersonalInfoChange}\n                required\n              />\n            </div>\n\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"company\">Company/Organization</Label>\n              <Input\n                id=\"company\"\n                name=\"company\"\n                value={personalInfoForm.company}\n                onChange={handlePersonalInfoChange}\n              />\n            </div>\n\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"location\">Location</Label>\n              <Input\n                id=\"location\"\n                name=\"location\"\n                value={personalInfoForm.location}\n                onChange={handlePersonalInfoChange}\n              />\n            </div>\n\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"website\">Website</Label>\n              <Input\n                type=\"url\"\n                id=\"website\"\n                name=\"website\"\n                value={personalInfoForm.website}\n                onChange={handlePersonalInfoChange}\n                placeholder=\"https://example.com\"\n              />\n            </div>\n          </div>\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => setIsEditingPersonalInfo(false)}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : 'Save Changes'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAce,SAAS;;IACtB,MAAM,EACJ,qBAAqB,EACrB,wBAAwB,EACxB,gBAAgB,EAChB,wBAAwB,EACxB,gBAAgB,EAChB,OAAO,EACR,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAuB,cAAc;kBACjD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,6LAAC;oBAAK,UAAU,CAAC;wBACf,EAAE,cAAc;wBAChB;oBACF;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,iBAAiB,QAAQ;4CAChC,UAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,iBAAiB,OAAO;4CAC/B,UAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,iBAAiB,QAAQ;4CAChC,UAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,iBAAiB,OAAO;4CAC/B,UAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,yBAAyB;8CACzC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;8CAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GArFwB;;QAQlB,oIAAA,CAAA,aAAU;;;KARQ", "debugId": null}}, {"offset": {"line": 3025, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/EditBioModal.js"], "sourcesContent": ["'use client';\n\nimport { useProfile } from '@/contexts/ProfileContext';\nimport { FiX } from 'react-icons/fi';\n\nexport default function EditBioModal() {\n  const {\n    isEditingBio,\n    setIsEditingBio,\n    bioForm,\n    handleBioChange,\n    saveBio,\n    loading\n  } = useProfile();\n\n  if (!isEditingBio) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg w-full max-w-md p-6 relative\">\n        <button\n          onClick={() => setIsEditingBio(false)}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700\"\n        >\n          <FiX className=\"text-xl\" />\n        </button>\n        \n        <h2 className=\"text-2xl font-bold mb-6\">Edit Bio</h2>\n        \n        <form onSubmit={(e) => {\n          e.preventDefault();\n          saveBio();\n        }}>\n          <div className=\"mb-6\">\n            <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Bio\n            </label>\n            <textarea\n              id=\"bio\"\n              name=\"bio\"\n              value={bioForm}\n              onChange={handleBioChange}\n              rows={5}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n              maxLength={300}\n            />\n            <p className=\"text-sm text-gray-500 mt-1\">\n              {bioForm.length}/300 characters\n            </p>\n          </div>\n          \n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={() => setIsEditingBio(false)}\n              className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Saving...' : 'Save Changes'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EACJ,YAAY,EACZ,eAAe,EACf,OAAO,EACP,eAAe,EACf,OAAO,EACP,OAAO,EACR,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS,IAAM,gBAAgB;oBAC/B,WAAU;8BAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,6LAAC;oBAAK,UAAU,CAAC;wBACf,EAAE,cAAc;wBAChB;oBACF;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAM,WAAU;8CAA+C;;;;;;8CAG9E,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,WAAW;;;;;;8CAEb,6LAAC;oCAAE,WAAU;;wCACV,QAAQ,MAAM;wCAAC;;;;;;;;;;;;;sCAIpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GAlEwB;;QAQlB,oIAAA,CAAA,aAAU;;;KARQ", "debugId": null}}, {"offset": {"line": 3180, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/EditProfilePhotoModal.js"], "sourcesContent": ["'use client';\n\nimport { useProfile } from '@/contexts/ProfileContext';\nimport { FiX, FiUpload } from 'react-icons/fi';\nimport { useRef } from 'react';\n\nexport default function EditProfilePhotoModal() {\n  const {\n    isEditingProfilePhoto,\n    setIsEditingProfilePhoto,\n    profilePhotoPreview,\n    handleProfilePhotoChange,\n    saveProfilePhoto,\n    loading\n  } = useProfile();\n\n  const fileInputRef = useRef(null);\n\n  if (!isEditingProfilePhoto) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg w-full max-w-md p-6 relative\">\n        <button\n          onClick={() => setIsEditingProfilePhoto(false)}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700\"\n        >\n          <FiX className=\"text-xl\" />\n        </button>\n        \n        <h2 className=\"text-2xl font-bold mb-6\">Change Profile Photo</h2>\n        \n        <div className=\"flex flex-col items-center mb-6\">\n          <div className=\"h-40 w-40 rounded-full overflow-hidden mb-4 border-4 border-white shadow-md\">\n            <img\n              src={profilePhotoPreview}\n              alt=\"Profile preview\"\n              className=\"w-full h-full object-cover\"\n            />\n          </div>\n          \n          <input\n            type=\"file\"\n            ref={fileInputRef}\n            onChange={handleProfilePhotoChange}\n            accept=\"image/*\"\n            className=\"hidden\"\n          />\n          \n          <button\n            type=\"button\"\n            onClick={() => fileInputRef.current.click()}\n            className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200\"\n          >\n            <FiUpload />\n            Upload New Photo\n          </button>\n        </div>\n        \n        <div className=\"text-sm text-gray-500 mb-6\">\n          <p>Recommended: Square image, at least 400x400 pixels.</p>\n          <p>Maximum file size: 5MB</p>\n        </div>\n        \n        <div className=\"flex justify-end space-x-3\">\n          <button\n            type=\"button\"\n            onClick={() => setIsEditingProfilePhoto(false)}\n            className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n          >\n            Cancel\n          </button>\n          <button\n            type=\"button\"\n            onClick={saveProfilePhoto}\n            disabled={loading}\n            className=\"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? 'Saving...' : 'Save Changes'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EACJ,qBAAqB,EACrB,wBAAwB,EACxB,mBAAmB,EACnB,wBAAwB,EACxB,gBAAgB,EAChB,OAAO,EACR,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,IAAI,CAAC,uBAAuB,OAAO;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS,IAAM,yBAAyB;oBACxC,WAAU;8BAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;;;;;;sCAId,6LAAC;4BACC,MAAK;4BACL,KAAK;4BACL,UAAU;4BACV,QAAO;4BACP,WAAU;;;;;;sCAGZ,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa,OAAO,CAAC,KAAK;4BACzC,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAE;;;;;;;;;;;;8BAGL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,yBAAyB;4BACxC,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMrC;GA9EwB;;QAQlB,oIAAA,CAAA,aAAU;;;KARQ", "debugId": null}}, {"offset": {"line": 3361, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/EditCoverPhotoModal.js"], "sourcesContent": ["'use client';\n\nimport { useProfile } from '@/contexts/ProfileContext';\nimport { FiX, FiUpload } from 'react-icons/fi';\nimport { useRef } from 'react';\n\nexport default function EditCoverPhotoModal() {\n  const {\n    isEditingCoverPhoto,\n    setIsEditingCoverPhoto,\n    coverPhotoPreview,\n    handleCoverPhotoChange,\n    saveCoverPhoto,\n    loading\n  } = useProfile();\n\n  const fileInputRef = useRef(null);\n\n  if (!isEditingCoverPhoto) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg w-full max-w-md p-6 relative\">\n        <button\n          onClick={() => setIsEditingCoverPhoto(false)}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700\"\n        >\n          <FiX className=\"text-xl\" />\n        </button>\n        \n        <h2 className=\"text-2xl font-bold mb-6\">Change Cover Photo</h2>\n        \n        <div className=\"flex flex-col items-center mb-6\">\n          <div className=\"h-40 w-full rounded-lg overflow-hidden mb-4 border border-gray-200 shadow-sm\">\n            <img\n              src={coverPhotoPreview}\n              alt=\"Cover preview\"\n              className=\"w-full h-full object-cover\"\n            />\n          </div>\n          \n          <input\n            type=\"file\"\n            ref={fileInputRef}\n            onChange={handleCoverPhotoChange}\n            accept=\"image/*\"\n            className=\"hidden\"\n          />\n          \n          <button\n            type=\"button\"\n            onClick={() => fileInputRef.current.click()}\n            className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200\"\n          >\n            <FiUpload />\n            Upload New Cover Photo\n          </button>\n        </div>\n        \n        <div className=\"text-sm text-gray-500 mb-6\">\n          <p>Recommended: 1200x240 pixels or larger.</p>\n          <p>Maximum file size: 10MB</p>\n        </div>\n        \n        <div className=\"flex justify-end space-x-3\">\n          <button\n            type=\"button\"\n            onClick={() => setIsEditingCoverPhoto(false)}\n            className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n          >\n            Cancel\n          </button>\n          <button\n            type=\"button\"\n            onClick={saveCoverPhoto}\n            disabled={loading}\n            className=\"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? 'Saving...' : 'Save Changes'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EACJ,mBAAmB,EACnB,sBAAsB,EACtB,iBAAiB,EACjB,sBAAsB,EACtB,cAAc,EACd,OAAO,EACR,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,IAAI,CAAC,qBAAqB,OAAO;IAEjC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS,IAAM,uBAAuB;oBACtC,WAAU;8BAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;;;;;;sCAId,6LAAC;4BACC,MAAK;4BACL,KAAK;4BACL,UAAU;4BACV,QAAO;4BACP,WAAU;;;;;;sCAGZ,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,aAAa,OAAO,CAAC,KAAK;4BACzC,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;;;;;gCAAG;;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAE;;;;;;;;;;;;8BAGL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,uBAAuB;4BACtC,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMrC;GA9EwB;;QAQlB,oIAAA,CAAA,aAAU;;;KARQ", "debugId": null}}, {"offset": {"line": 3542, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/components/Profile/PrivacySettingsModal.js"], "sourcesContent": ["'use client';\n\nimport { useProfile } from '@/contexts/ProfileContext';\nimport { FiX } from 'react-icons/fi';\nimport { useState } from 'react';\n\nexport default function PrivacySettingsModal() {\n  const {\n    isEditingPrivacySettings,\n    setIsEditingPrivacySettings,\n    loading\n  } = useProfile();\n\n  const [privacySettings, setPrivacySettings] = useState({\n    profileVisibility: 'public',\n    showEmail: false,\n    showLocation: true,\n    allowMessages: true,\n    allowTagging: true,\n    allowComments: true\n  });\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setPrivacySettings(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const saveSettings = () => {\n    // Simulate API call\n    setTimeout(() => {\n      setIsEditingPrivacySettings(false);\n    }, 1000);\n  };\n\n  if (!isEditingPrivacySettings) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg w-full max-w-md p-6 relative\">\n        <button\n          onClick={() => setIsEditingPrivacySettings(false)}\n          className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700\"\n        >\n          <FiX className=\"text-xl\" />\n        </button>\n        \n        <h2 className=\"text-2xl font-bold mb-6\">Privacy Settings</h2>\n        \n        <form onSubmit={(e) => {\n          e.preventDefault();\n          saveSettings();\n        }}>\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Profile Visibility\n            </label>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  id=\"public\"\n                  name=\"profileVisibility\"\n                  value=\"public\"\n                  checked={privacySettings.profileVisibility === 'public'}\n                  onChange={handleChange}\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500\"\n                />\n                <label htmlFor=\"public\" className=\"ml-2 text-sm text-gray-700\">\n                  Public (Anyone can view your profile)\n                </label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  id=\"followers\"\n                  name=\"profileVisibility\"\n                  value=\"followers\"\n                  checked={privacySettings.profileVisibility === 'followers'}\n                  onChange={handleChange}\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500\"\n                />\n                <label htmlFor=\"followers\" className=\"ml-2 text-sm text-gray-700\">\n                  Followers Only (Only people who follow you can view your profile)\n                </label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  id=\"private\"\n                  name=\"profileVisibility\"\n                  value=\"private\"\n                  checked={privacySettings.profileVisibility === 'private'}\n                  onChange={handleChange}\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500\"\n                />\n                <label htmlFor=\"private\" className=\"ml-2 text-sm text-gray-700\">\n                  Private (Only you can view your profile)\n                </label>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"mb-6 space-y-3\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Information Visibility\n            </label>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"showEmail\"\n                name=\"showEmail\"\n                checked={privacySettings.showEmail}\n                onChange={handleChange}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 rounded\"\n              />\n              <label htmlFor=\"showEmail\" className=\"ml-2 text-sm text-gray-700\">\n                Show email address on profile\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"showLocation\"\n                name=\"showLocation\"\n                checked={privacySettings.showLocation}\n                onChange={handleChange}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 rounded\"\n              />\n              <label htmlFor=\"showLocation\" className=\"ml-2 text-sm text-gray-700\">\n                Show location on profile\n              </label>\n            </div>\n          </div>\n          \n          <div className=\"mb-6 space-y-3\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Interaction Settings\n            </label>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"allowMessages\"\n                name=\"allowMessages\"\n                checked={privacySettings.allowMessages}\n                onChange={handleChange}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 rounded\"\n              />\n              <label htmlFor=\"allowMessages\" className=\"ml-2 text-sm text-gray-700\">\n                Allow direct messages\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"allowTagging\"\n                name=\"allowTagging\"\n                checked={privacySettings.allowTagging}\n                onChange={handleChange}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 rounded\"\n              />\n              <label htmlFor=\"allowTagging\" className=\"ml-2 text-sm text-gray-700\">\n                Allow others to tag you in posts\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"allowComments\"\n                name=\"allowComments\"\n                checked={privacySettings.allowComments}\n                onChange={handleChange}\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 rounded\"\n              />\n              <label htmlFor=\"allowComments\" className=\"ml-2 text-sm text-gray-700\">\n                Allow comments on your posts\n              </label>\n            </div>\n          </div>\n          \n          <div className=\"flex justify-end space-x-3\">\n            <button\n              type=\"button\"\n              onClick={() => setIsEditingPrivacySettings(false)}\n              className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Saving...' : 'Save Changes'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EACJ,wBAAwB,EACxB,2BAA2B,EAC3B,OAAO,EACR,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,mBAAmB;QACnB,WAAW;QACX,cAAc;QACd,eAAe;QACf,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,oBAAoB;QACpB,WAAW;YACT,4BAA4B;QAC9B,GAAG;IACL;IAEA,IAAI,CAAC,0BAA0B,OAAO;IAEtC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS,IAAM,4BAA4B;oBAC3C,WAAU;8BAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,6LAAC;oBAAK,UAAU,CAAC;wBACf,EAAE,cAAc;wBAChB;oBACF;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAM;oDACN,SAAS,gBAAgB,iBAAiB,KAAK;oDAC/C,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAS,WAAU;8DAA6B;;;;;;;;;;;;sDAIjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAM;oDACN,SAAS,gBAAgB,iBAAiB,KAAK;oDAC/C,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAA6B;;;;;;;;;;;;sDAIpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAM;oDACN,SAAS,gBAAgB,iBAAiB,KAAK;oDAC/C,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAOtE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAIhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,SAAS,gBAAgB,SAAS;4CAClC,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA6B;;;;;;;;;;;;8CAKpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,SAAS,gBAAgB,YAAY;4CACrC,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAMzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAIhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,SAAS,gBAAgB,aAAa;4CACtC,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAA6B;;;;;;;;;;;;8CAKxE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,SAAS,gBAAgB,YAAY;4CACrC,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAA6B;;;;;;;;;;;;8CAKvE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,SAAS,gBAAgB,aAAa;4CACtC,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAM1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,4BAA4B;oCAC3C,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GAzMwB;;QAKlB,oIAAA,CAAA,aAAU;;;KALQ", "debugId": null}}, {"offset": {"line": 3981, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/app/profile/page.js"], "sourcesContent": ["'use client';\r\n\r\nimport CoverPhoto from '@/components/Profile/CoverPhoto';\r\nimport ProfileAvatar from '@/components/Profile/ProfileAvatar';\r\nimport ProfileHeader from '@/components/Profile/ProfileHeader';\r\nimport ProfileInfo from '@/components/Profile/ProfileInfo';\r\nimport ProfileStats from '@/components/Profile/ProfileStats';\r\nimport ProfileNav from '@/components/Profile/ProfileNav';\r\nimport PostsSection from '@/components/Profile/PostsSection';\r\nimport { ProfileProvider } from '@/contexts/ProfileContext';\r\nimport EditProfileModal from '@/components/Profile/EditProfileModal';\r\nimport EditBioModal from '@/components/Profile/EditBioModal';\r\nimport EditProfilePhotoModal from '@/components/Profile/EditProfilePhotoModal';\r\nimport EditCoverPhotoModal from '@/components/Profile/EditCoverPhotoModal';\r\nimport PrivacySettingsModal from '@/components/Profile/PrivacySettingsModal';\r\nimport { Toaster } from 'sonner';\r\n\r\nexport default function Profile() {\r\n  return (\r\n    <ProfileProvider>\r\n      <div className=\"w-[1200px] p-6 font-sans bg-gray-50 mx-auto\">\r\n        <div className=\"relative mb-16\">\r\n          <CoverPhoto />\r\n          <ProfileAvatar />\r\n          <ProfileHeader />\r\n        </div>\r\n        <div className=\"px-8\">\r\n          <ProfileInfo />\r\n          <ProfileStats />\r\n          <ProfileNav />\r\n          <PostsSection />\r\n        </div>\r\n\r\n        {/* Modals for editing profile */}\r\n        <EditProfileModal />\r\n        <EditBioModal />\r\n        <EditProfilePhotoModal />\r\n        <EditCoverPhotoModal />\r\n        <PrivacySettingsModal />\r\n\r\n        {/* Toast notifications */}\r\n        <Toaster position=\"top-right\" richColors />\r\n      </div>\r\n    </ProfileProvider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;AAiBe,SAAS;IACtB,qBACE,6LAAC,oIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6IAAA,CAAA,UAAU;;;;;sCACX,6LAAC,gJAAA,CAAA,UAAa;;;;;sCACd,6LAAC,gJAAA,CAAA,UAAa;;;;;;;;;;;8BAEhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8IAAA,CAAA,UAAW;;;;;sCACZ,6LAAC,+IAAA,CAAA,UAAY;;;;;sCACb,6LAAC,6IAAA,CAAA,UAAU;;;;;sCACX,6LAAC,+IAA<PERSON>,CAAA,UAAY;;;;;;;;;;;8BAIf,6LAAC,mJAAA,CAAA,UAAgB;;;;;8BACjB,6LAAC,+IAAA,CAAA,UAAY;;;;;8BACb,6LAAC,wJAAA,CAAA,UAAqB;;;;;8BACtB,6LAAC,sJAAA,CAAA,UAAmB;;;;;8BACpB,6LAAC,uJAAA,CAAA,UAAoB;;;;;8BAGrB,6LAAC,2IAAA,CAAA,UAAO;oBAAC,UAAS;oBAAY,UAAU;;;;;;;;;;;;;;;;;AAIhD;KA5BwB", "debugId": null}}]}
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Chatting Model
model Chatting {
  id        String    @id @default(cuid())
  from      String
  to        String
  createdAt DateTime  @default(now())
  messages  Message[]

  fromUser  User      @relation(name: "FromUser", fields: [from], references: [id])
  toUser    User      @relation(name: "ToUser", fields: [to], references: [id])
}

model Message {
  id         String    @id @default(cuid())
  messager   String
  content    String
  chattingId String?
  chatting   Chatting? @relation(fields: [chattingId], references: [id])
}

// Comment Model
model Comment {
  id         String    @id @default(cuid())
  postId     String
  text       String
  createdBy  String
  approved   Bo<PERSON><PERSON>   @default(false)
  createdAt  DateTime  @default(now())
  likes      J<PERSON>      @default("[]")
  replies    Reply[]   @relation

  post       Post      @relation(fields: [postId], references: [id])
  user       User      @relation(fields: [createdBy], references: [id])
}

model Reply {
  id         String    @id @default(cuid())
  text       String?
  createdAt  DateTime  @default(now())
  likes      <PERSON><PERSON>      @default("[]")
  commentId  String
  createdBy  String

  comment    Comment   @relation(fields: [commentId], references: [id], onDelete: Cascade)
  user       User      @relation(fields: [createdBy], references: [id])
}

enum PostStatus {
  APPROVED
  UNAPPROVED
}

model Post {
  id         String     @id @default(cuid())
  title      String
  content    String
  authorId   String
  imageUrl   String?
  date       DateTime   @default(now())
  status     PostStatus @default(APPROVED)
  comments   Comment[]
  votes      Vote[]
  categories Category[]
  challenges Challenge[]
  loves      PostLove[]

  author     User       @relation(fields: [authorId], references: [id])
}

model Category {
  id         String   @id @default(cuid())
  name       String   @unique
  posts      Post[]   // Many-to-many relation with Post
}

model Vote {
  id         String    @id @default(cuid())
  userId     String
  postId     String
  voteType   String
  createdAt  DateTime  @default(now())

  user       User      @relation(fields: [userId], references: [id])
  post       Post      @relation(fields: [postId], references: [id])
}
enum Role {
  writer
  admin
  user
}
// Notification Model
model Notification {
  id           String         @id @default(cuid())
  userId       String         // User receiving the notification
  type         NotificationType // Type of notification (COMMENT, LIKE, NEW_POST)
  relatedId    String         // ID of the related entity (comment, post, etc.)
  relatedType  String         // Type of related entity ("Comment", "Post", etc.)
  message      String         // Notification message
  isRead       Boolean        @default(false)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([isRead])
}

// Notification Type Enum
enum NotificationType {
  COMMENT        // When someone comments on user's post
  LIKE           // When someone likes user's post/comment
  NEW_POST       // When a followed user creates a new post
}

// Updated User Model with lastNotificationViewedAt
model User {
  id                    String          @id @default(cuid())
  username              String          @unique
  email                 String          @unique
  password              String
  role                  Role            @default(user)
  profilePic            String          @default("")
  coverPic              String          @default("")
  bio                   String?         @default("")
  verified              Boolean         @default(false)
  otpCode               String?         @default("")
  lastNotificationViewedAt DateTime?    // New field to track last notification view
  comments              Comment[]
  replies               Reply[]
  sentChats             Chatting[]      @relation(name: "FromUser")
  receivedChats         Chatting[]      @relation(name: "ToUser")
  posts                 Post[]
  votes                 Vote[]
  challenges            Challenge[]
  otps                  OTP[]
  lovedPosts            PostLove[]
  activityLogs          ActivityLog[]
  notifications         Notification[]  // Notifications relation
  followers             Follow[]        @relation("Followers")
  following             Follow[]        @relation("Following")
}

model OTP {
  id         String    @id @default(cuid())
  code       String
  expiresAt  DateTime
  createdAt  DateTime  @default(now())
  isUsed     Boolean   @default(false)
  userId     String
  user       User      @relation(fields: [userId], references: [id])
}

model Follow {
  id           String @id @default(cuid())
  followerId   String // the user who is following
  followingId  String // the user who is being followed

  follower     User   @relation("Following", fields: [followerId], references: [id])
  following    User   @relation("Followers", fields: [followingId], references: [id])

  @@unique([followerId, followingId]) // Prevent duplicate follows
}

model Challenge {
  id          String   @id @default(cuid())
  postId      String
  userId      String
  content     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  post        Post     @relation(fields: [postId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
}

// Role Enum
model PostLove {
  id        String   @id @default(cuid())
  userId    String
  postId    String
  createdAt DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id])
  post      Post     @relation(fields: [postId], references: [id])

  @@unique([userId, postId])
}



enum AdminAction {
  CREATE
  UPDATE
  DELETE
  APPROVE
  REJECT
  BAN
  UNBAN
  OTHER
}

model ActivityLog {
  id          String      @id @default(cuid())
  adminId     String
  actionType  AdminAction
  targetId    String
  targetType  String      // e.g., "user", "post", "comment"
  details     Json        // Structured data about the action
  metadata    Json?       // Additional context information
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  admin       User        @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@index([adminId])
  @@index([actionType])
  @@index([targetType])
}

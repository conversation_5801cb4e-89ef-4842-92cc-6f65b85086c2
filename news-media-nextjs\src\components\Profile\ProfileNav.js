'use client';

import { <PERSON>FileText, FiHeart, FiAward, FiClock, FiBookmark } from 'react-icons/fi';
import { useProfile } from '@/contexts/ProfileContext';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ProfileNav() {
  const { activeTab, setActiveTab } = useProfile();

  const navItems = [
    { icon: <FiFileText />, label: 'Posts' },
    { icon: <FiHeart />, label: 'Loved' },
    { icon: <FiAward />, label: 'Challenges' },
    { icon: <FiClock />, label: 'Activity' },
    { icon: <FiBookmark />, label: 'Saved' },
  ];

  return (
    <div className="border-b border-gray-200">
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="bg-transparent h-auto p-0 w-full justify-start overflow-x-auto">
          {navItems.map((item, index) => (
            <TabsTrigger
              key={index}
              value={item.label}
              className="px-4 py-2 data-[state=active]:border-b-2 data-[state=active]:border-primary-500 data-[state=active]:text-primary-600 data-[state=active]:bg-transparent data-[state=active]:shadow-none rounded-none flex items-center gap-2"
            >
              {item.icon}
              {item.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
}
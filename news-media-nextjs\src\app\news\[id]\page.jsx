"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { formatDistanceToNow, format } from "date-fns";
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  ThumbsUp, 
  ThumbsDown, 
  Bookmark,
  Send,
  User,
  Calendar,
  Tag,
  Eye
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { cn } from "@/lib/utils";
import { Noto_Sans_Bengali } from "next/font/google";
import NewsCard from "@/components/News/NewsCard";

const bengali = Noto_Sans_Bengali({ 
  subsets: ["bengali"],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
});

export default function NewsDetailPage() {
  const params = useParams();
  const id = params.id;
  
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [comments, setComments] = useState([]);
  const [commentText, setCommentText] = useState("");
  const [submittingComment, setSubmittingComment] = useState(false);
  const [voteStatus, setVoteStatus] = useState(null); // null, 'upvote', or 'downvote'
  const [voteCount, setVoteCount] = useState(0);
  const [relatedPosts, setRelatedPosts] = useState([]);
  const [loadingRelated, setLoadingRelated] = useState(true);

  // Fetch post data
  useEffect(() => {
    if (id) {
      fetchPostData();
      fetchComments();
    }
  }, [id]);

  // Fetch related posts when post category is available
  useEffect(() => {
    if (post?.category) {
      fetchRelatedPosts();
    }
  }, [post?.category]);

  const fetchPostData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/posts?id=${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch post data');
      }
      const data = await response.json();
      setPost(data);
      setLikeCount(data.loves?.length || 0);
      setVoteCount(
        (data.votes?.filter(v => v.type === 'upvote')?.length || 0) - 
        (data.votes?.filter(v => v.type === 'downvote')?.length || 0)
      );
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/comments/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch comments');
      }
      const data = await response.json();
      setComments(data);
    } catch (err) {
      console.error('Error fetching comments:', err);
    }
  };

  const fetchRelatedPosts = async () => {
    try {
      setLoadingRelated(true);
      // In a real app, you would fetch related posts by category
      // For now, we'll just fetch the latest posts
      const response = await fetch('/api/posts?limit=3');
      if (!response.ok) {
        throw new Error('Failed to fetch related posts');
      }
      const data = await response.json();
      // Filter out the current post
      const filtered = data.filter(p => p.id !== id);
      setRelatedPosts(filtered.slice(0, 3));
      setLoadingRelated(false);
    } catch (err) {
      console.error('Error fetching related posts:', err);
      setLoadingRelated(false);
    }
  };

  const handleLike = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login or show login modal
        return;
      }

      const response = await fetch(`/api/post-love/${post.id}`, {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setIsLiked(true);
        setLikeCount(prev => prev + 1);
      } else if (response.status === 400) {
        // Already liked, so unlike
        const unlikeResponse = await fetch(`/api/post-love/${post.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          }
        });
        
        if (unlikeResponse.ok) {
          setIsLiked(false);
          setLikeCount(prev => prev - 1);
        }
      }
    } catch (err) {
      console.error('Error toggling like:', err);
    }
  };

  const handleVote = async (voteType) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login or show login modal
        return;
      }

      const response = await fetch('/api/vote/vote', {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          postId: post.id,
          voteType: voteType
        })
      });

      if (response.ok) {
        // If successful, update UI
        if (voteStatus === voteType) {
          // Remove vote if clicking the same button
          setVoteStatus(null);
          setVoteCount(prev => voteType === 'upvote' ? prev - 1 : prev + 1);
        } else {
          // Change vote or add new vote
          setVoteStatus(voteType);
          if (voteStatus === null) {
            // Adding new vote
            setVoteCount(prev => voteType === 'upvote' ? prev + 1 : prev - 1);
          } else {
            // Changing vote (from down to up or vice versa)
            setVoteCount(prev => voteType === 'upvote' ? prev + 2 : prev - 2);
          }
        }
      }
    } catch (err) {
      console.error('Error voting:', err);
    }
  };

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    if (!commentText.trim()) return;

    try {
      setSubmittingComment(true);
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login or show login modal
        return;
      }

      const response = await fetch(`/api/comments/${post.id}`, {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: commentText })
      });

      if (response.ok) {
        const newComment = await response.json();
        setComments(prev => [newComment.comment, ...prev]);
        setCommentText('');
      }
    } catch (err) {
      console.error('Error submitting comment:', err);
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: post.title,
          text: `Check out this post: ${post.title}`,
          url: `${window.location.origin}/news/${post.id}`
        });
      } else {
        // Fallback for browsers that don't support the Web Share API
        navigator.clipboard.writeText(`${window.location.origin}/news/${post.id}`);
        // Show toast notification
        // toast.success("Link copied to clipboard!");
      }
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    // Implement bookmark functionality with backend
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <Skeleton className="h-8 w-3/4 mb-4" />
          <div className="flex items-center gap-2 mb-6">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div>
              <Skeleton className="h-4 w-32 mb-1" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <Skeleton className="h-96 w-full mb-8 rounded-xl" />
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto bg-card rounded-xl p-6 shadow-md">
          <p className="text-destructive text-center">Error loading post: {error}</p>
          <div className="flex justify-center mt-4">
            <Button onClick={fetchPostData}>Retry</Button>
          </div>
        </div>
      </div>
    );
  }

  if (!post) return null;

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Article Header */}
        <h1 className={cn("text-3xl md:text-4xl font-bold mb-4", bengali.className)}>
          {post.title}
        </h1>

        {/* Author and Meta Info */}
        <div className="flex flex-wrap items-center gap-6 mb-6 text-muted-foreground">
          <div className="flex items-center gap-3">
            <Link href={`/profile/${post.author?.id}`}>
              <div className="relative h-12 w-12 rounded-full overflow-hidden border border-border">
                {post.author?.profilePic ? (
                  <Image 
                    src={post.author.profilePic} 
                    alt={post.author.username}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold">
                    {post.author?.username?.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
            </Link>
            <div>
              <Link href={`/profile/${post.author?.id}`} className="font-medium hover:underline">
                {post.author?.username}
              </Link>
              <p className="text-xs">
                {post.author?.role === 'writer' ? 'Writer' : 'Contributor'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span className="text-sm">
              {post.date ? format(new Date(post.date), 'MMMM dd, yyyy') : 'Recently'}
            </span>
          </div>

          {post.category && (
            <div className="flex items-center gap-1">
              <Tag className="h-4 w-4" />
              <span className="text-sm">{post.category}</span>
            </div>
          )}

          <div className="flex items-center gap-1">
            <Eye className="h-4 w-4" />
            <span className="text-sm">1.2K views</span>
          </div>
        </div>

        {/* Featured Image */}
        {post.imageUrl && (
          <div className="relative h-[400px] w-full mb-8 rounded-xl overflow-hidden shadow-md">
            <Image 
              src={post.imageUrl.startsWith('./') ? `/api${post.imageUrl.substring(1)}` : post.imageUrl} 
              alt={post.title}
              fill
              className="object-cover"
              priority
            />
          </div>
        )}

        {/* Article Content */}
        <div className={cn("prose prose-lg max-w-none mb-8", bengali.className)}>
          <div className="whitespace-pre-line">
            {post.content}
          </div>
        </div>

        {/* Action Bar */}
        <div className="flex flex-wrap items-center justify-between gap-4 py-4 border-t border-b border-border/50 mb-8">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm" 
              className={cn(
                "flex items-center gap-2 rounded-full", 
                isLiked ? "text-red-500" : ""
              )}
              onClick={handleLike}
            >
              <Heart className={cn("h-5 w-5", isLiked ? "fill-red-500" : "")} />
              <span>{likeCount}</span>
            </Button>

            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                className={cn(
                  "rounded-full", 
                  voteStatus === 'upvote' ? "text-primary" : ""
                )}
                onClick={() => handleVote('upvote')}
              >
                <ThumbsUp className={cn("h-5 w-5", voteStatus === 'upvote' ? "fill-primary" : "")} />
              </Button>
              <span className="mx-1">{voteCount}</span>
              <Button 
                variant="ghost" 
                size="sm" 
                className={cn(
                  "rounded-full", 
                  voteStatus === 'downvote' ? "text-destructive" : ""
                )}
                onClick={() => handleVote('downvote')}
              >
                <ThumbsDown className={cn("h-5 w-5", voteStatus === 'downvote' ? "fill-destructive" : "")} />
              </Button>
            </div>

            <Button 
              variant="ghost" 
              size="sm" 
              className="flex items-center gap-2 rounded-full"
              onClick={() => document.getElementById('comments-section').scrollIntoView({ behavior: 'smooth' })}
            >
              <MessageCircle className="h-5 w-5" />
              <span>{comments.length}</span>
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="sm" 
              className="flex items-center gap-2 rounded-full"
              onClick={handleShare}
            >
              <Share2 className="h-5 w-5" />
              Share
            </Button>

            <Button 
              variant="ghost" 
              size="sm" 
              className={cn(
                "rounded-full", 
                isBookmarked ? "text-primary" : ""
              )}
              onClick={toggleBookmark}
            >
              <Bookmark className={cn("h-5 w-5", isBookmarked ? "fill-primary" : "")} />
            </Button>
          </div>
        </div>

        {/* Author Bio */}
        {post.author && (
          <div className="bg-card rounded-xl p-6 shadow-md mb-8">
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4">
              <div className="relative h-20 w-20 rounded-full overflow-hidden border-2 border-primary/30">
                {post.author.profilePic ? (
                  <Image 
                    src={post.author.profilePic} 
                    alt={post.author.username}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold text-2xl">
                    {post.author.username?.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              <div className="text-center sm:text-left">
                <h3 className="text-xl font-semibold mb-1">{post.author.username}</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  {post.author.role === 'writer' ? 'Writer' : 'Contributor'} at Pothoczuto News Media
                </p>
                <p className="text-sm">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
                <div className="mt-3">
                  <Link href={`/profile/${post.author.id}`}>
                    <Button variant="outline" size="sm">View Profile</Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Comments Section */}
        <div id="comments-section" className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Comments ({comments.length})</h2>
          
          {/* Comment Form */}
          <form onSubmit={handleSubmitComment} className="mb-6">
            <div className="relative">
              <textarea
                placeholder="Write a comment..."
                className="w-full px-4 py-3 rounded-lg bg-accent/50 border border-border focus:outline-none focus:ring-2 focus:ring-primary/30 min-h-24"
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                disabled={submittingComment}
              />
              <Button 
                type="submit" 
                className="mt-2 flex items-center gap-2"
                disabled={submittingComment || !commentText.trim()}
              >
                {submittingComment ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    <span>Post Comment</span>
                  </>
                )}
              </Button>
            </div>
          </form>

          {/* Comments List */}
          <div className="space-y-6">
            {comments.length > 0 ? (
              comments.map((comment) => (
                <div key={comment.id} className="bg-card rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="relative h-10 w-10 rounded-full overflow-hidden border border-border">
                      {comment.user?.profilePic ? (
                        <Image 
                          src={comment.user.profilePic} 
                          alt={comment.user.username}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold">
                          {comment.user?.username?.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>
                    <div>
                      <span className="font-medium">{comment.user?.username}</span>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                  <p className={cn("mb-3", bengali.className)}>{comment.text}</p>
                  
                  {/* Comment Actions */}
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <button className="hover:text-foreground">Like</button>
                    <button className="hover:text-foreground">Reply</button>
                  </div>

                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-4 pl-6 border-l-2 border-border/50 space-y-4">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="bg-accent/20 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="relative h-8 w-8 rounded-full overflow-hidden border border-border">
                              {reply.user?.profilePic ? (
                                <Image 
                                  src={reply.user.profilePic} 
                                  alt={reply.user.username}
                                  fill
                                  className="object-cover"
                                />
                              ) : (
                                <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold text-xs">
                                  {reply.user?.username?.charAt(0).toUpperCase()}
                                </div>
                              )}
                            </div>
                            <div>
                              <span className="font-medium text-sm">{reply.user?.username}</span>
                              <p className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
                              </p>
                            </div>
                          </div>
                          <p className={cn("text-sm", bengali.className)}>{reply.text}</p>
                          
                          {/* Reply Actions */}
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            <button className="hover:text-foreground">Like</button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-8 bg-accent/20 rounded-lg">
                <User className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                <p className="text-muted-foreground">No comments yet. Be the first to comment!</p>
              </div>
            )}
          </div>
        </div>

        {/* Related Posts */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Related Posts</h2>
          
          {loadingRelated ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-card rounded-xl overflow-hidden shadow-md">
                  <Skeleton className="h-48 w-full" />
                  <div className="p-4">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              ))}
            </div>
          ) : relatedPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <NewsCard 
                  key={relatedPost.id} 
                  initialData={relatedPost} 
                  compact={true} 
                />
              ))}
            </div>
          ) : (
            <p className="text-center py-4 text-muted-foreground">No related posts found.</p>
          )}
        </div>
      </div>
    </div>
  );
}

'use client';

import { FiEdit, FiMail, FiUserPlus, FiShare2, FiCamera, FiEye, FiUserCheck, FiCopy, FiTwitter, FiFacebook, FiLinkedin } from 'react-icons/fi';
import { useProfile } from '@/contexts/ProfileContext';
// No need for useState with shadcn dropdowns
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

export default function ProfileHeader() {
  const {
    setIsEditingPersonalInfo,
    setIsEditingProfilePhoto,
    setIsEditingCoverPhoto,
    setIsEditingBio,
    setIsEditingPrivacySettings,
    isFollowing,
    toggleFollow
  } = useProfile();

  // No need for state variables with shadcn dropdowns

  const editOptions = [
    {
      icon: <FiUserPlus />,
      text: 'Edit personal info',
      onClick: () => {
        setIsEditingPersonalInfo(true);
        setIsDetailsOpen(false);
      }
    },
    {
      icon: <FiCamera />,
      text: 'Change profile photo',
      onClick: () => {
        setIsEditingProfilePhoto(true);
        setIsDetailsOpen(false);
      }
    },
    {
      icon: <FiCamera />,
      text: 'Change cover photo',
      onClick: () => {
        setIsEditingCoverPhoto(true);
        setIsDetailsOpen(false);
      }
    },
    {
      icon: <FiEdit />,
      text: 'Update bio',
      onClick: () => {
        setIsEditingBio(true);
        setIsDetailsOpen(false);
      }
    },
    {
      icon: <FiEye />,
      text: 'Privacy settings',
      onClick: () => {
        setIsEditingPrivacySettings(true);
        setIsDetailsOpen(false);
      }
    },
  ];

  // Share options are now directly in the dropdown menu

  const copyProfileLink = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success('Profile link copied to clipboard!');
  };

  const shareOnTwitter = () => {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out this profile!`, '_blank');
    toast.success('Shared on Twitter');
  };

  const shareOnFacebook = () => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`, '_blank');
    toast.success('Shared on Facebook');
  };

  const shareOnLinkedIn = () => {
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`, '_blank');
    toast.success('Shared on LinkedIn');
  };

  return (
    <div className="flex justify-end gap-3 mt-6">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="rounded-full flex items-center gap-2">
            <FiEdit className="h-4 w-4" />
            Edit Profile
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          {editOptions.map((option, index) => (
            <DropdownMenuItem
              key={index}
              onClick={option.onClick}
              className="cursor-pointer flex items-center gap-2"
            >
              {option.icon}
              <span>{option.text}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <Button
        variant="default"
        className="rounded-full"
        onClick={() => toast.info('Message feature coming soon!')}
      >
        <FiMail className="mr-2 h-4 w-4" />
        Message
      </Button>

      <Button
        variant={isFollowing ? "outline" : "secondary"}
        className="rounded-full"
        onClick={toggleFollow}
      >
        {isFollowing ? <FiUserCheck className="mr-2 h-4 w-4" /> : <FiUserPlus className="mr-2 h-4 w-4" />}
        {isFollowing ? 'Following' : 'Follow'}
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon" className="rounded-full">
            <FiShare2 className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={copyProfileLink} className="cursor-pointer">
            <FiCopy className="mr-2 h-4 w-4" />
            <span>Copy profile link</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={shareOnTwitter} className="cursor-pointer">
            <FiTwitter className="mr-2 h-4 w-4" />
            <span>Share on Twitter</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={shareOnFacebook} className="cursor-pointer">
            <FiFacebook className="mr-2 h-4 w-4" />
            <span>Share on Facebook</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={shareOnLinkedIn} className="cursor-pointer">
            <FiLinkedin className="mr-2 h-4 w-4" />
            <span>Share on LinkedIn</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
{"name": "blog-auth-system", "version": "1.0.0", "description": "A secure blog authentication system using Node.js, MongoDB, JWT, bcrypt, and Joi.", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@prisma/client": "^6.5.0", "axios": "^1.8.4", "bcrypt": "^5.1.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.1.4", "express": "^4.18.2", "joi": "^17.8.0", "jsonwebtoken": "^9.0.0", "module-alias": "^2.2.3", "mongoose": "^7.3.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.0.1", "prisma": "^6.5.0"}, "_moduleAliases": {"@": "src"}}
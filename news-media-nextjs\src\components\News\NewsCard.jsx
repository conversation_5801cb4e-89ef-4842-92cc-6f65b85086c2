
"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import {
  Heart,
  MessageCircle,
  Share2,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal,
  Bookmark,
  Send
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { cn } from "@/lib/utils";
import { Noto_Sans_Bengali } from "next/font/google";

const bengali = Noto_Sans_Bengali({
  subsets: ["bengali"],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
});

function NewsCard({
  id,
  initialData = null,
  compact = false,
  className
}) {
  const [post, setPost] = useState(initialData);
  const [loading, setLoading] = useState(!initialData);
  const [error, setError] = useState(null);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [comments, setComments] = useState([]);
  const [commentText, setCommentText] = useState("");
  const [submittingComment, setSubmittingComment] = useState(false);
  const [voteStatus, setVoteStatus] = useState(null); // null, 'up', or 'down'
  const [voteCount, setVoteCount] = useState(0);

  // Fetch post data if not provided initially
  useEffect(() => {
    if (!initialData && id) {
      fetchPostData();
    } else if (initialData) {
      setLikeCount(initialData.loves?.length || 0);
      setVoteCount(
        (initialData.votes?.filter(v => v.type === 'upvote')?.length || 0) -
        (initialData.votes?.filter(v => v.type === 'downvote')?.length || 0)
      );
    }
  }, [id, initialData]);

  const fetchPostData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/posts?id=${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch post data');
      }
      const data = await response.json();
      setPost(data);
      setLikeCount(data.loves?.length || 0);
      setVoteCount(
        (data.votes?.filter(v => v.type === 'upvote')?.length || 0) -
        (data.votes?.filter(v => v.type === 'downvote')?.length || 0)
      );
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const fetchComments = async () => {
    if (!showComments && id) {
      try {
        const response = await fetch(`/api/comments/${id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch comments');
        }
        const data = await response.json();
        setComments(data);
        setShowComments(true);
      } catch (err) {
        console.error('Error fetching comments:', err);
      }
    } else {
      setShowComments(!showComments);
    }
  };

  const handleLike = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login or show login modal
        return;
      }

      const response = await fetch(`/api/post-love/${post.id}`, {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setIsLiked(true);
        setLikeCount(prev => prev + 1);
      } else if (response.status === 400) {
        // Already liked, so unlike
        const unlikeResponse = await fetch(`/api/post-love/${post.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          }
        });

        if (unlikeResponse.ok) {
          setIsLiked(false);
          setLikeCount(prev => prev - 1);
        }
      }
    } catch (err) {
      console.error('Error toggling like:', err);
    }
  };

  const handleVote = async (voteType) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login or show login modal
        return;
      }

      const response = await fetch('/api/vote/vote', {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          postId: post.id,
          voteType: voteType
        })
      });

      if (response.ok) {
        // If successful, update UI
        if (voteStatus === voteType) {
          // Remove vote if clicking the same button
          setVoteStatus(null);
          setVoteCount(prev => voteType === 'upvote' ? prev - 1 : prev + 1);
        } else {
          // Change vote or add new vote
          setVoteStatus(voteType);
          if (voteStatus === null) {
            // Adding new vote
            setVoteCount(prev => voteType === 'upvote' ? prev + 1 : prev - 1);
          } else {
            // Changing vote (from down to up or vice versa)
            setVoteCount(prev => voteType === 'upvote' ? prev + 2 : prev - 2);
          }
        }
      }
    } catch (err) {
      console.error('Error voting:', err);
    }
  };

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    if (!commentText.trim()) return;

    try {
      setSubmittingComment(true);
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login or show login modal
        return;
      }

      const response = await fetch(`/api/comments/${post.id}`, {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: commentText })
      });

      if (response.ok) {
        const newComment = await response.json();
        setComments(prev => [newComment.comment, ...prev]);
        setCommentText('');
      }
    } catch (err) {
      console.error('Error submitting comment:', err);
    } finally {
      setSubmittingComment(false);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: post.title,
          text: `Check out this post: ${post.title}`,
          url: `${window.location.origin}/news/${post.id}`
        });
      } else {
        // Fallback for browsers that don't support the Web Share API
        navigator.clipboard.writeText(`${window.location.origin}/news/${post.id}`);
        // Show toast notification
        // toast.success("Link copied to clipboard!");
      }
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    // Implement bookmark functionality with backend
  };

  if (loading) {
    return (
      <div className={cn("bg-card rounded-xl overflow-hidden shadow-md", className)}>
        <div className="p-4">
          <Skeleton className="h-6 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2 mb-4" />
          <Skeleton className="h-48 w-full mb-4" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4 mb-4" />
          <div className="flex justify-between mt-4">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("bg-card rounded-xl overflow-hidden shadow-md p-4", className)}>
        <p className="text-destructive">Error loading post: {error}</p>
        <Button onClick={fetchPostData} className="mt-2">Retry</Button>
      </div>
    );
  }

  if (!post) return null;

  return (
    <div className={cn("bg-card rounded-xl overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg", className)}>
      {/* Post Header */}
      <div className="p-4 flex items-center justify-between border-b border-border/50">
        <div className="flex items-center gap-3">
          <Link href={`/profile/${post.author?.id}`}>
            <div className="relative h-10 w-10 rounded-full overflow-hidden border border-border">
              {post.author?.profilePic ? (
                <Image
                  src={post.author.profilePic}
                  alt={post.author.username}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold">
                  {post.author?.username?.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
          </Link>
          <div>
            <Link href={`/profile/${post.author?.id}`} className="font-medium hover:underline">
              {post.author?.username}
            </Link>
            <p className="text-xs text-muted-foreground">
              {post.date ? formatDistanceToNow(new Date(post.date), { addSuffix: true }) : 'Recently'}
              {post.category && <span> • {post.category}</span>}
            </p>
          </div>
        </div>
        <Button variant="ghost" size="icon" className="rounded-full">
          <MoreHorizontal className="h-5 w-5" />
        </Button>
      </div>

      {/* Post Content */}
      <div className="p-4">
        <Link href={`/news/${post.id}`}>
          <h2 className={cn("text-xl font-semibold mb-2 hover:text-primary transition-colors", bengali.className)}>
            {post.title}
          </h2>
        </Link>

        {!compact && (
          <div className={cn("text-muted-foreground mb-4 line-clamp-3", bengali.className)}>
            {post.content}
          </div>
        )}

        {post.imageUrl && (
          <div className="relative h-64 w-full mb-4 rounded-lg overflow-hidden">
            <Image
              src={post.imageUrl.startsWith('./') ? `/api${post.imageUrl.substring(1)}` : post.imageUrl}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        )}
      </div>

      {/* Post Stats */}
      <div className="px-4 py-2 flex items-center justify-between text-sm text-muted-foreground border-t border-border/50">
        <div className="flex items-center gap-2">
          <Heart className={cn("h-4 w-4", isLiked ? "fill-red-500 text-red-500" : "")} />
          <span>{likeCount} {likeCount === 1 ? 'love' : 'loves'}</span>
        </div>
        <div className="flex items-center gap-4">
          <span>{comments.length} comments</span>
          <span>{voteCount} votes</span>
        </div>
      </div>

      {/* Post Actions */}
      <div className="px-4 py-2 flex items-center justify-between border-t border-border/50">
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "flex items-center gap-2 rounded-full",
            isLiked ? "text-red-500" : ""
          )}
          onClick={handleLike}
        >
          <Heart className={cn("h-5 w-5", isLiked ? "fill-red-500" : "")} />
          Love
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 rounded-full"
          onClick={fetchComments}
        >
          <MessageCircle className="h-5 w-5" />
          Comment
        </Button>

        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "rounded-full",
              voteStatus === 'upvote' ? "text-primary" : ""
            )}
            onClick={() => handleVote('upvote')}
          >
            <ThumbsUp className={cn("h-5 w-5", voteStatus === 'upvote' ? "fill-primary" : "")} />
          </Button>
          <span className="mx-1">{voteCount}</span>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "rounded-full",
              voteStatus === 'downvote' ? "text-destructive" : ""
            )}
            onClick={() => handleVote('downvote')}
          >
            <ThumbsDown className={cn("h-5 w-5", voteStatus === 'downvote' ? "fill-destructive" : "")} />
          </Button>
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 rounded-full"
          onClick={handleShare}
        >
          <Share2 className="h-5 w-5" />
          Share
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "rounded-full",
            isBookmarked ? "text-primary" : ""
          )}
          onClick={toggleBookmark}
        >
          <Bookmark className={cn("h-5 w-5", isBookmarked ? "fill-primary" : "")} />
        </Button>
      </div>

      {/* Comments Section */}
      {showComments && (
        <div className="p-4 border-t border-border/50">
          {/* Comment Form */}
          <form onSubmit={handleSubmitComment} className="mb-4 flex gap-2">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Write a comment..."
                className="w-full px-4 py-2 pr-10 rounded-full bg-accent/50 border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                disabled={submittingComment}
              />
              <Button
                type="submit"
                size="icon"
                variant="ghost"
                className="absolute right-1 top-1/2 -translate-y-1/2 rounded-full h-8 w-8"
                disabled={submittingComment || !commentText.trim()}
              >
                {submittingComment ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </form>

          {/* Comments List */}
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {comments.length > 0 ? (
              comments.map((comment) => (
                <div key={comment.id} className="bg-accent/30 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="relative h-8 w-8 rounded-full overflow-hidden border border-border">
                      {comment.user?.profilePic ? (
                        <Image
                          src={comment.user.profilePic}
                          alt={comment.user.username}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold text-xs">
                          {comment.user?.username?.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>
                    <div>
                      <span className="font-medium text-sm">{comment.user?.username}</span>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                  <p className={cn("text-sm", bengali.className)}>{comment.text}</p>

                  {/* Comment Actions */}
                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                    <button className="hover:text-foreground">Like</button>
                    <button className="hover:text-foreground">Reply</button>
                  </div>

                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-2 pl-4 border-l-2 border-border/50 space-y-2">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="bg-accent/20 rounded-lg p-2">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="relative h-6 w-6 rounded-full overflow-hidden border border-border">
                              {reply.user?.profilePic ? (
                                <Image
                                  src={reply.user.profilePic}
                                  alt={reply.user.username}
                                  fill
                                  className="object-cover"
                                />
                              ) : (
                                <div className="h-full w-full bg-primary/20 flex items-center justify-center text-primary font-bold text-xs">
                                  {reply.user?.username?.charAt(0).toUpperCase()}
                                </div>
                              )}
                            </div>
                            <span className="font-medium text-xs">{reply.user?.username}</span>
                          </div>
                          <p className={cn("text-xs", bengali.className)}>{reply.text}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <p className="text-center text-muted-foreground">No comments yet. Be the first to comment!</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default NewsCard;
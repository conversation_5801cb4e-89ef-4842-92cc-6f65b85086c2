'use client';

import { useProfile } from '@/contexts/ProfileContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';

export default function EditProfileModal() {
  const {
    isEditingPersonalInfo,
    setIsEditingPersonalInfo,
    personalInfoForm,
    handlePersonalInfoChange,
    savePersonalInfo,
    loading
  } = useProfile();

  return (
    <Dialog open={isEditingPersonalInfo} onOpenChange={setIsEditingPersonalInfo}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Personal Information</DialogTitle>
        </DialogHeader>

        <form onSubmit={(e) => {
          e.preventDefault();
          savePersonalInfo();
        }}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="username">Name</Label>
              <Input
                id="username"
                name="username"
                value={personalInfoForm.username}
                onChange={handlePersonalInfoChange}
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="company">Company/Organization</Label>
              <Input
                id="company"
                name="company"
                value={personalInfoForm.company}
                onChange={handlePersonalInfoChange}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                name="location"
                value={personalInfoForm.location}
                onChange={handlePersonalInfoChange}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="website">Website</Label>
              <Input
                type="url"
                id="website"
                name="website"
                value={personalInfoForm.website}
                onChange={handlePersonalInfoChange}
                placeholder="https://example.com"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditingPersonalInfo(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

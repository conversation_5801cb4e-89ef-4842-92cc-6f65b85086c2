'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export function ScrollProgress({ className, color = 'bg-primary' }) {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateScrollProgress = () => {
      // Calculate how far the user has scrolled down the page
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = scrollTop / docHeight;
      setScrollProgress(scrollPercent);
    };

    // Add scroll event listener
    window.addEventListener('scroll', updateScrollProgress);
    
    // Initial calculation
    updateScrollProgress();
    
    // Clean up
    return () => window.removeEventListener('scroll', updateScrollProgress);
  }, []);

  return (
    <div className={cn(
      "fixed top-0 left-0 right-0 h-1 z-50 transition-opacity duration-300",
      scrollProgress > 0 ? "opacity-100" : "opacity-0",
      className
    )}>
      <div 
        className={cn("h-full", color)}
        style={{ width: `${scrollProgress * 100}%` }}
      />
    </div>
  );
}

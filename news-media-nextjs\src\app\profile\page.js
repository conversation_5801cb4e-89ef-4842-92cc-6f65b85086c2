'use client';

import CoverPhoto from '@/components/Profile/CoverPhoto';
import ProfileAvatar from '@/components/Profile/ProfileAvatar';
import ProfileHeader from '@/components/Profile/ProfileHeader';
import ProfileInfo from '@/components/Profile/ProfileInfo';
import ProfileStats from '@/components/Profile/ProfileStats';
import ProfileNav from '@/components/Profile/ProfileNav';
import PostsSection from '@/components/Profile/PostsSection';
import { ProfileProvider } from '@/contexts/ProfileContext';
import EditProfileModal from '@/components/Profile/EditProfileModal';
import EditBioModal from '@/components/Profile/EditBioModal';
import EditProfilePhotoModal from '@/components/Profile/EditProfilePhotoModal';
import EditCoverPhotoModal from '@/components/Profile/EditCoverPhotoModal';
import PrivacySettingsModal from '@/components/Profile/PrivacySettingsModal';
import { Toaster } from 'sonner';

export default function Profile() {
  return (
    <ProfileProvider>
      <div className="w-[1200px] p-6 font-sans bg-gray-50 mx-auto">
        <div className="relative mb-16">
          <CoverPhoto />
          <ProfileAvatar />
          <ProfileHeader />
        </div>
        <div className="px-8">
          <ProfileInfo />
          <ProfileStats />
          <ProfileNav />
          <PostsSection />
        </div>

        {/* Modals for editing profile */}
        <EditProfileModal />
        <EditBioModal />
        <EditProfilePhotoModal />
        <EditCoverPhotoModal />
        <PrivacySettingsModal />

        {/* Toast notifications */}
        <Toaster position="top-right" richColors />
      </div>
    </ProfileProvider>
  );
}
'use client';

import { FiCamera } from 'react-icons/fi';
import { useProfile } from '@/contexts/ProfileContext';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';

export default function ProfileAvatar() {
  const {
    userData,
    profilePhotoPreview,
    setIsEditingProfilePhoto
  } = useProfile();

  return (
    <div className="absolute -bottom-12 left-8 flex items-end">
      <div className="relative group">
        <Avatar className="h-[140px] w-[140px] border-4 border-white bg-white shadow-md">
          <AvatarImage
            src={profilePhotoPreview}
            alt={`${userData.username}'s avatar`}
            className="object-cover"
          />
          <AvatarFallback className="text-2xl">
            {userData.username.split(' ').map(name => name[0]).join('')}
          </AvatarFallback>
        </Avatar>
        <Button
          variant="ghost"
          size="icon"
          className="absolute inset-0 bg-black/30 rounded-full opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-300"
          onClick={() => setIsEditingProfilePhoto(true)}
        >
          <FiCamera className="text-white text-3xl" />
        </Button>
      </div>
      <div className="ml-4 mb-4 flex items-center">
        <span className="bg-gradient-to-r from-primary-500 to-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
          Senior Editor
        </span>
      </div>
    </div>
  );
}
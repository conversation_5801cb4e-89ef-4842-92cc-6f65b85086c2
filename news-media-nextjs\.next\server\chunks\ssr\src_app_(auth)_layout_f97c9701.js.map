{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/news%20media%20project/news-media-nextjs/src/app/%28auth%29/layout.js"], "sourcesContent": ["'use client';\n\nexport default function RootLayout({ children }) {\n      <html lang=\"en\" suppressHydrationWarning>\n      <head>\n        <link rel=\"shortcut icon\" href=\"/favicon.ico\" type=\"image/x-icon\" />\n      </head>\n      <body\n       \n      >\n        \n \n          {children}\n \n        \n\n\n      </body>\n    </html>\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;kBACzC,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACxC,8OAAC;0BACC,cAAA,8OAAC;oBAAK,KAAI;oBAAgB,MAAK;oBAAe,MAAK;;;;;;;;;;;0BAErD,8OAAC;0BAKI;;;;;;;;;;;;AAOX", "debugId": null}}]}